import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  const configService = app.get(ConfigService);
  const logger = new Logger('Bootstrap');

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    crossOriginEmbedderPolicy: false,
  }));

  // Compression middleware
  app.use(compression());

  // Global pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Global filters
  app.useGlobalFilters(new HttpExceptionFilter());

  // Global interceptors
  app.useGlobalInterceptors(
    new LoggingInterceptor(),
    new TransformInterceptor(),
  );

  // CORS configuration
  app.enableCors({
    origin: configService.get('CORS_ORIGINS')?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
  });

  // API prefix
  app.setGlobalPrefix('api/v1');

  // Swagger documentation
  if (configService.get('NODE_ENV') !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('PHCityRent API')
      .setDescription(`
        🏠 **Enterprise-Grade Real Estate Platform API for Port Harcourt**

        **89 RESTful Endpoints** | **25 Database Tables** | **84+ Indexes** | **Production Ready**

        ## Features
        - 🔐 **JWT Authentication** with refresh tokens
        - 🏘️ **Property Management** with advanced search
        - 💰 **Payment Processing** with multiple gateways
        - 📊 **Analytics & Reporting** with real-time data
        - 🤖 **AI-Powered Recommendations**
        - 📱 **Real-time Notifications**
        - 📁 **File Management** with secure storage
        - 👥 **Agent Management** system
        - 🛡️ **Enterprise Security** with RBAC

        ## Database Integration
        - **Supabase Compatible** with 25 pre-built migrations
        - **PostgreSQL** with advanced indexing
        - **Real-time subscriptions** enabled
        - **Row Level Security** (RLS) policies

        ## Getting Started
        1. Visit the **Test** endpoints to verify API functionality
        2. Use **Authentication** endpoints to get JWT tokens
        3. Explore **Properties** endpoints for core functionality
        4. Check **Health** endpoints for system monitoring
      `)
      .setVersion('1.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addTag('Authentication', 'User authentication and authorization endpoints')
      .addTag('Users', 'User management and profile operations')
      .addTag('Properties', 'Property listing and management operations')
      .addTag('Agents', 'Real estate agent management operations')
      .addTag('Payments', 'Payment processing and transaction operations')
      .addTag('Analytics', 'Analytics, reporting and dashboard operations')
      .addTag('Admin', 'Administrative and system management operations')
      .addTag('Files', 'File upload and document management operations')
      .addTag('Notifications', 'Notification and messaging system operations')
      .addTag('Health', 'System health monitoring and diagnostics')
      .addTag('Test', 'API testing and development utilities')
      .addTag('Database Migration', 'Supabase to Backend migration utilities')
      .addServer('http://localhost:3001', 'Development server')
      .addServer('https://api.phcityrent.com', 'Production server')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
    });

    logger.log('📚 Swagger documentation available at /api/docs');
  }

  // Graceful shutdown
  process.on('SIGTERM', async () => {
    logger.log('SIGTERM received, shutting down gracefully');
    await app.close();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('SIGINT received, shutting down gracefully');
    await app.close();
    process.exit(0);
  });

  const port = configService.get('PORT') || 3001;
  await app.listen(port);

  logger.log(`🚀 PHCityRent API is running on: http://localhost:${port}`);
  logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  logger.log(`🌍 Environment: ${configService.get('NODE_ENV')}`);
  logger.log(`🗄️ Database: ${configService.get('DB_HOST')}:${configService.get('DB_PORT')}`);
}

bootstrap().catch((error) => {
  console.error('❌ Error starting server:', error);
  process.exit(1);
});
