import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToMany,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';

export enum UserRole {
  ADMIN = 'admin',
  AGENT = 'agent',
  LANDLORD = 'landlord',
  TENANT = 'tenant',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING_VERIFICATION = 'pending_verification',
}

@Entity('users')
@Index(['email'], { unique: true })
@Index(['phone'], { unique: true })
export class User {
  @ApiProperty({
    description: 'Unique identifier for the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @Column({ unique: true })
  @Index()
  email: string;

  @ApiHideProperty()
  @Column()
  @Exclude()
  password: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
  })
  @Column()
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
  })
  @Column()
  lastName: string;

  @ApiProperty({
    description: 'User phone number',
    example: '+2348012345678',
  })
  @Column({ unique: true })
  @Index()
  phone: string;

  @ApiProperty({
    description: 'User role in the system',
    enum: UserRole,
    example: UserRole.TENANT,
  })
  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.TENANT,
  })
  @Index()
  role: UserRole;

  @ApiProperty({
    description: 'User status',
    enum: UserStatus,
    example: UserStatus.ACTIVE,
  })
  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.PENDING_VERIFICATION,
  })
  @Index()
  status: UserStatus;

  @ApiProperty({
    description: 'Whether the user account is active',
    example: true,
  })
  @Column({ default: true })
  @Index()
  isActive: boolean;

  @ApiProperty({
    description: 'Whether the user email is verified',
    example: false,
  })
  @Column({ default: false })
  isEmailVerified: boolean;

  @ApiProperty({
    description: 'Whether the user phone is verified',
    example: false,
  })
  @Column({ default: false })
  isPhoneVerified: boolean;

  @ApiProperty({
    description: 'User profile picture URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @Column({ nullable: true })
  avatar?: string;

  @ApiProperty({
    description: 'User date of birth',
    example: '1990-01-01',
    required: false,
  })
  @Column({ type: 'date', nullable: true })
  dateOfBirth?: Date;

  @ApiProperty({
    description: 'User gender',
    example: 'male',
    required: false,
  })
  @Column({ nullable: true })
  gender?: string;

  @ApiProperty({
    description: 'User address',
    example: '123 Main Street, Port Harcourt',
    required: false,
  })
  @Column({ nullable: true })
  address?: string;

  @ApiProperty({
    description: 'User city',
    example: 'Port Harcourt',
    required: false,
  })
  @Column({ nullable: true })
  city?: string;

  @ApiProperty({
    description: 'User state',
    example: 'Rivers',
    required: false,
  })
  @Column({ nullable: true })
  state?: string;

  @ApiProperty({
    description: 'User country',
    example: 'Nigeria',
    required: false,
  })
  @Column({ default: 'Nigeria' })
  country: string;

  @ApiProperty({
    description: 'User occupation',
    example: 'Software Engineer',
    required: false,
  })
  @Column({ nullable: true })
  occupation?: string;

  @ApiProperty({
    description: 'User monthly income',
    example: 500000,
    required: false,
  })
  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  monthlyIncome?: number;

  @ApiProperty({
    description: 'User preferred language',
    example: 'en',
  })
  @Column({ default: 'en' })
  preferredLanguage: string;

  @ApiProperty({
    description: 'User timezone',
    example: 'Africa/Lagos',
  })
  @Column({ default: 'Africa/Lagos' })
  timezone: string;

  @ApiProperty({
    description: 'Whether user wants to receive email notifications',
    example: true,
  })
  @Column({ default: true })
  emailNotifications: boolean;

  @ApiProperty({
    description: 'Whether user wants to receive SMS notifications',
    example: true,
  })
  @Column({ default: true })
  smsNotifications: boolean;

  @ApiProperty({
    description: 'Whether user wants to receive push notifications',
    example: true,
  })
  @Column({ default: true })
  pushNotifications: boolean;

  @ApiHideProperty()
  @Column({ nullable: true })
  @Exclude()
  emailVerificationToken?: string;

  @ApiHideProperty()
  @Column({ nullable: true })
  @Exclude()
  emailVerificationExpires?: Date;

  @ApiHideProperty()
  @Column({ nullable: true })
  @Exclude()
  phoneVerificationToken?: string;

  @ApiHideProperty()
  @Column({ nullable: true })
  @Exclude()
  phoneVerificationExpires?: Date;

  @ApiHideProperty()
  @Column({ nullable: true })
  @Exclude()
  passwordResetToken?: string;

  @ApiHideProperty()
  @Column({ nullable: true })
  @Exclude()
  passwordResetExpires?: Date;

  @ApiProperty({
    description: 'Last login timestamp',
    example: '2023-12-01T10:00:00Z',
    required: false,
  })
  @Column({ nullable: true })
  lastLoginAt?: Date;

  @ApiProperty({
    description: 'Last login IP address',
    example: '***********',
    required: false,
  })
  @Column({ nullable: true })
  lastLoginIp?: string;

  @ApiProperty({
    description: 'Number of failed login attempts',
    example: 0,
  })
  @Column({ default: 0 })
  failedLoginAttempts: number;

  @ApiProperty({
    description: 'Account locked until timestamp',
    example: null,
    required: false,
  })
  @Column({ nullable: true })
  lockedUntil?: Date;

  @ApiProperty({
    description: 'User metadata as JSON',
    example: { preferences: { theme: 'dark' } },
    required: false,
  })
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'When the user was created',
    example: '2023-12-01T10:00:00Z',
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the user was last updated',
    example: '2023-12-01T10:00:00Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany('Property', 'landlord')
  landlordProperties: any[];

  @OneToMany('Property', 'agent')
  agentProperties: any[];

  // Virtual properties
  @ApiProperty({
    description: 'User full name',
    example: 'John Doe',
  })
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  @ApiProperty({
    description: 'Whether the account is locked',
    example: false,
  })
  get isLocked(): boolean {
    return this.lockedUntil && this.lockedUntil > new Date();
  }

  @ApiProperty({
    description: 'Whether the user is fully verified',
    example: false,
  })
  get isVerified(): boolean {
    return this.isEmailVerified && this.isPhoneVerified;
  }
}
