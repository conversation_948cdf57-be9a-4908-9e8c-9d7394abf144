import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';

export class AuthResponse {
  @ApiProperty({
    description: 'User information',
    type: () => User,
  })
  user: Partial<User>;

  @ApiProperty({
    description: 'JWT access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token for obtaining new access tokens',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  refreshToken: string;
}
