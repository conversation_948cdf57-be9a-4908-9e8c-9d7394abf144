import { DataSource } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User, UserRole, UserStatus } from '../../modules/users/entities/user.entity';

export class UserSeeder {
  public static async run(dataSource: DataSource): Promise<void> {
    const userRepository = dataSource.getRepository(User);

    // Check if admin user already exists
    const existingAdmin = await userRepository.findOne({
      where: { email: '<EMAIL>' },
    });

    if (existingAdmin) {
      console.log('Admin user already exists, skipping user seeding');
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('Admin123!@#', 12);

    // Create admin user
    const adminUser = userRepository.create({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator',
      phone: '+2348000000001',
      role: UserRole.ADMIN,
      status: UserStatus.ACTIVE,
      isActive: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      city: 'Port Harcourt',
      state: 'Rivers',
      country: 'Nigeria',
    });

    await userRepository.save(adminUser);

    // Create sample agent user
    const agentUser = userRepository.create({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Sample',
      lastName: 'Agent',
      phone: '+2348000000002',
      role: UserRole.AGENT,
      status: UserStatus.ACTIVE,
      isActive: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      city: 'Port Harcourt',
      state: 'Rivers',
      country: 'Nigeria',
      occupation: 'Real Estate Agent',
    });

    await userRepository.save(agentUser);

    // Create sample landlord user
    const landlordUser = userRepository.create({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Sample',
      lastName: 'Landlord',
      phone: '+2348000000003',
      role: UserRole.LANDLORD,
      status: UserStatus.ACTIVE,
      isActive: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      city: 'Port Harcourt',
      state: 'Rivers',
      country: 'Nigeria',
      occupation: 'Property Owner',
    });

    await userRepository.save(landlordUser);

    // Create sample tenant user
    const tenantUser = userRepository.create({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Sample',
      lastName: 'Tenant',
      phone: '+2348000000004',
      role: UserRole.TENANT,
      status: UserStatus.ACTIVE,
      isActive: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      city: 'Port Harcourt',
      state: 'Rivers',
      country: 'Nigeria',
      occupation: 'Software Engineer',
      monthlyIncome: 500000,
    });

    await userRepository.save(tenantUser);

    console.log('✅ User seeding completed successfully');
    console.log('📧 Admin credentials: <EMAIL> / Admin123!@#');
    console.log('📧 Agent credentials: <EMAIL> / Admin123!@#');
    console.log('📧 Landlord credentials: <EMAIL> / Admin123!@#');
    console.log('📧 Tenant credentials: <EMAIL> / Admin123!@#');
  }
}
