import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../../../modules/users/entities/user.entity';
import { Property } from '../../properties/entities/property.entity';

export enum PaymentType {
  RENT = 'rent',
  DEPOSIT = 'deposit',
  COMMISSION = 'commission',
  SERVICE_FEE = 'service_fee',
  SUBSCRIPTION = 'subscription',
}

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentGateway {
  PAYSTACK = 'paystack',
  FLUTTERWAVE = 'flutterwave',
  BANK_TRANSFER = 'bank_transfer',
  CASH = 'cash',
}

@Entity('payments')
@Index(['status'])
@Index(['paymentType'])
@Index(['gateway'])
@Index(['createdAt'])
@Index(['userId'])
@Index(['propertyId'])
export class Payment {
  @ApiProperty({
    description: 'Unique identifier for the payment',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Payment reference number',
    example: 'PAY_20231201_001',
  })
  @Column({ unique: true })
  @Index()
  reference: string;

  @ApiProperty({
    description: 'Payment type',
    enum: PaymentType,
    example: PaymentType.RENT,
  })
  @Column({
    type: 'enum',
    enum: PaymentType,
  })
  @Index()
  paymentType: PaymentType;

  @ApiProperty({
    description: 'Payment status',
    enum: PaymentStatus,
    example: PaymentStatus.COMPLETED,
  })
  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  @Index()
  status: PaymentStatus;

  @ApiProperty({
    description: 'Payment gateway used',
    enum: PaymentGateway,
    example: PaymentGateway.PAYSTACK,
  })
  @Column({
    type: 'enum',
    enum: PaymentGateway,
  })
  @Index()
  gateway: PaymentGateway;

  @ApiProperty({
    description: 'Payment amount in Naira',
    example: 1200000,
  })
  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'NGN',
  })
  @Column({ default: 'NGN' })
  currency: string;

  @ApiProperty({
    description: 'Payment description',
    example: 'Annual rent payment for 3-bedroom apartment',
  })
  @Column()
  description: string;

  @ApiProperty({
    description: 'Gateway transaction reference',
    example: 'T_123456789',
    required: false,
  })
  @Column({ nullable: true })
  gatewayReference?: string;

  @ApiProperty({
    description: 'Gateway response data',
    required: false,
  })
  @Column({ type: 'jsonb', nullable: true })
  gatewayResponse?: Record<string, any>;

  @ApiProperty({
    description: 'Payment due date',
    example: '2023-12-31T23:59:59Z',
    required: false,
  })
  @Column({ nullable: true })
  dueDate?: Date;

  @ApiProperty({
    description: 'Payment completion date',
    example: '2023-12-01T10:00:00Z',
    required: false,
  })
  @Column({ nullable: true })
  paidAt?: Date;

  @ApiProperty({
    description: 'Payment failure reason',
    required: false,
  })
  @Column({ nullable: true })
  failureReason?: string;

  @ApiProperty({
    description: 'User who made the payment',
    type: () => User,
  })
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'user_id' })
  @Index()
  userId: string;

  @ApiProperty({
    description: 'Property associated with payment',
    type: () => Property,
    required: false,
  })
  @ManyToOne(() => Property, { eager: false, nullable: true })
  @JoinColumn({ name: 'property_id' })
  property?: Property;

  @Column({ name: 'property_id', nullable: true })
  @Index()
  propertyId?: string;

  @ApiProperty({
    description: 'Recipient user (for commission payments)',
    type: () => User,
    required: false,
  })
  @ManyToOne(() => User, { eager: false, nullable: true })
  @JoinColumn({ name: 'recipient_id' })
  recipient?: User;

  @Column({ name: 'recipient_id', nullable: true })
  recipientId?: string;

  @ApiProperty({
    description: 'Payment metadata',
    required: false,
  })
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'When the payment was created',
    example: '2023-12-01T10:00:00Z',
  })
  @CreateDateColumn()
  @Index()
  createdAt: Date;

  @ApiProperty({
    description: 'When the payment was last updated',
    example: '2023-12-01T10:00:00Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  @ApiProperty({
    description: 'Whether payment is overdue',
    example: false,
  })
  get isOverdue(): boolean {
    return this.dueDate && this.dueDate < new Date() && this.status === PaymentStatus.PENDING;
  }

  @ApiProperty({
    description: 'Whether payment is successful',
    example: true,
  })
  get isSuccessful(): boolean {
    return this.status === PaymentStatus.COMPLETED;
  }
}
