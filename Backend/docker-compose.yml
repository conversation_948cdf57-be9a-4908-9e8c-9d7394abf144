version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: phcityrent-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: phcityrent
      POSTGRES_USER: phcityrent_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secure_password_123}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/postgresql.production.conf:/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    networks:
      - phcityrent-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U phcityrent_user -d phcityrent"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: phcityrent-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password_123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - phcityrent-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PHCityRent API
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: phcityrent-api
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: phcityrent_user
      DB_PASSWORD: ${DB_PASSWORD:-secure_password_123}
      DB_NAME: phcityrent
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis_password_123}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET:-your-super-secret-refresh-key-change-in-production}
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - phcityrent-network
    volumes:
      - api_logs:/app/logs
      - api_uploads:/app/uploads
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: phcityrent-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    networks:
      - phcityrent-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  api_logs:
    driver: local
  api_uploads:
    driver: local

networks:
  phcityrent-network:
    driver: bridge
