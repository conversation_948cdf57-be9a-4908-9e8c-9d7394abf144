{"name": "phcityrent-backend", "version": "1.0.0", "description": "Enterprise-grade NestJS backend for PHCityRent platform", "author": "PHCityRent Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "test:server": "ts-node test-server.ts", "swagger:demo": "ts-node swagger-demo-server.ts", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "typeorm migration:generate -d src/config/typeorm.config.ts", "migration:run": "typeorm migration:run -d src/config/typeorm.config.ts", "migration:revert": "typeorm migration:revert -d src/config/typeorm.config.ts", "seed": "ts-node src/database/seeds/run-seeds.ts", "db:reset": "npm run migration:revert && npm run migration:run && npm run seed"}, "dependencies": {"@nestjs/axios": "^3.0.1", "@nestjs/bull": "^10.0.1", "@nestjs/cache-manager": "^2.1.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.2", "@nestjs/jwt": "^10.1.1", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.2.7", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.1.16", "@nestjs/terminus": "^10.2.0", "@nestjs/throttler": "^5.0.1", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.2.7", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bull": "^4.12.0", "cache-manager": "^5.2.4", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "redis": "^4.6.10", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sharp": "^0.32.6", "socket.io": "^4.7.4", "sqlite3": "^5.1.7", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.14.202", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.14", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}