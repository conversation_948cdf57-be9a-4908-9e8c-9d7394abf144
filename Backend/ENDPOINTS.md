# PHCityRent API Endpoints

## Complete API Endpoint List (50+ RESTful Endpoints)

### **Authentication Module** (8 endpoints)
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout
- `PATCH /api/v1/auth/change-password` - Change password
- `POST /api/v1/auth/forgot-password` - Request password reset
- `POST /api/v1/auth/reset-password` - Reset password with token
- `GET /api/v1/auth/me` - Get current user profile

### **Users Module** (8 endpoints)
- `GET /api/v1/users` - Get all users (Admin/Agent only)
- `GET /api/v1/users/profile` - Get current user profile
- `GET /api/v1/users/:id` - Get user by ID (Admin/Agent only)
- `PATCH /api/v1/users/profile` - Update current user profile
- `PATCH /api/v1/users/:id` - Update user by ID (Admin only)
- `PATCH /api/v1/users/:id/activate` - Activate user (Admin only)
- `PATCH /api/v1/users/:id/deactivate` - Deactivate user (Admin only)
- `DELETE /api/v1/users/:id` - Delete user (Admin only)

### **Properties Module** (11 endpoints)
- `POST /api/v1/properties` - Create new property
- `GET /api/v1/properties` - Search and filter properties
- `GET /api/v1/properties/my-properties` - Get current user properties
- `GET /api/v1/properties/stats` - Get property statistics
- `GET /api/v1/properties/:id` - Get property by ID
- `PATCH /api/v1/properties/:id` - Update property
- `PATCH /api/v1/properties/:id/status` - Update property status
- `PATCH /api/v1/properties/:id/toggle-featured` - Toggle featured status
- `PATCH /api/v1/properties/:id/toggle-verified` - Toggle verified status
- `POST /api/v1/properties/:id/inquire` - Record property inquiry
- `DELETE /api/v1/properties/:id` - Delete property

### **Agents Module** (8 endpoints)
- `GET /api/v1/agents` - Get all active agents
- `GET /api/v1/agents/top` - Get top performing agents
- `GET /api/v1/agents/search` - Search agents by name/email
- `GET /api/v1/agents/:id` - Get agent by ID
- `GET /api/v1/agents/:id/properties` - Get properties managed by agent
- `GET /api/v1/agents/:id/stats` - Get agent performance statistics
- `PATCH /api/v1/agents/:id/activate` - Activate agent
- `PATCH /api/v1/agents/:id/deactivate` - Deactivate agent

### **Payments Module** (8 endpoints)
- `POST /api/v1/payments` - Create new payment
- `GET /api/v1/payments` - Get all payments
- `GET /api/v1/payments/stats` - Get payment statistics
- `GET /api/v1/payments/overdue` - Get overdue payments
- `GET /api/v1/payments/by-type/:type` - Get payments by type
- `GET /api/v1/payments/:id` - Get payment by ID
- `PATCH /api/v1/payments/:id/status` - Update payment status
- `POST /api/v1/payments/:id/process` - Process payment with gateway response

### **Analytics Module** (5 endpoints)
- `GET /api/v1/analytics/dashboard` - Get dashboard statistics
- `GET /api/v1/analytics/properties` - Get property analytics
- `GET /api/v1/analytics/users` - Get user analytics
- `GET /api/v1/analytics/payments` - Get payment analytics
- `GET /api/v1/analytics/market-insights` - Get market insights and trends

### **Admin Module** (15 endpoints)
- `GET /api/v1/admin/overview` - Get system overview and statistics
- `GET /api/v1/admin/users` - Get all users with pagination
- `GET /api/v1/admin/properties` - Get all properties with pagination
- `GET /api/v1/admin/payments` - Get all payments with pagination
- `GET /api/v1/admin/pending-approvals` - Get items pending approval
- `GET /api/v1/admin/recent-activity` - Get recent system activity
- `PATCH /api/v1/admin/users/:id/suspend` - Suspend a user
- `PATCH /api/v1/admin/users/:id/activate` - Activate a user
- `PATCH /api/v1/admin/properties/:id/verify` - Verify a property
- `PATCH /api/v1/admin/properties/:id/unverify` - Unverify a property
- `PATCH /api/v1/admin/properties/:id/feature` - Feature a property
- `PATCH /api/v1/admin/properties/:id/unfeature` - Unfeature a property
- `PATCH /api/v1/admin/payments/:id/approve` - Approve a payment
- `PATCH /api/v1/admin/payments/:id/reject` - Reject a payment
- `POST /api/v1/admin/bulk-actions` - Perform bulk actions

### **Files Module** (9 endpoints)
- `POST /api/v1/files/upload/image` - Upload a single image
- `POST /api/v1/files/upload/images` - Upload multiple images
- `POST /api/v1/files/upload/document` - Upload a document
- `GET /api/v1/files/images/:userId/:filename` - Get an image file
- `GET /api/v1/files/documents/:userId/:filename` - Get a document file
- `GET /api/v1/files/my-files` - Get current user files
- `GET /api/v1/files/my-files/stats` - Get current user file statistics
- `DELETE /api/v1/files/images/:filename` - Delete an image file
- `DELETE /api/v1/files/documents/:filename` - Delete a document file

### **Notifications Module** (8 endpoints)
- `GET /api/v1/notifications` - Get all notifications
- `GET /api/v1/notifications/my-notifications` - Get current user notifications
- `GET /api/v1/notifications/unread-count` - Get unread notifications count
- `GET /api/v1/notifications/stats` - Get notification statistics
- `GET /api/v1/notifications/by-type/:type` - Get notifications by type
- `GET /api/v1/notifications/:id` - Get notification by ID
- `PATCH /api/v1/notifications/:id/read` - Mark notification as read
- `POST /api/v1/notifications/mark-all-read` - Mark all notifications as read
- `POST /api/v1/notifications/retry-failed` - Retry failed notifications

### **Health Module** (4 endpoints)
- `GET /api/v1/health` - Comprehensive health check
- `GET /api/v1/health/database` - Database health check
- `GET /api/v1/health/memory` - Memory health check
- `GET /api/v1/health/disk` - Disk health check

## **Total: 84 RESTful Endpoints** ✅

## API Features Summary

### **Authentication & Security**
- JWT with refresh tokens
- Role-based access control (RBAC)
- Password security and reset
- Account lockout protection
- Rate limiting and throttling

### **Property Management**
- Comprehensive CRUD operations
- Advanced search and filtering
- Property analytics and statistics
- Featured and verified properties
- Location-based search with coordinates
- Image and media support

### **User Management**
- Multi-role user system (Admin, Agent, Landlord, Tenant)
- User profile management
- Agent performance tracking
- User activity monitoring

### **Payment Processing**
- Multiple payment types and gateways
- Payment status tracking
- Overdue payment management
- Payment analytics and reporting
- Commission tracking

### **Analytics & Reporting**
- Dashboard statistics
- Property market analytics
- User behavior analytics
- Payment analytics
- Market insights and trends

### **File Management**
- Image upload with automatic resizing
- Document upload and management
- File security and access control
- User file statistics

### **Notification System**
- Multi-channel notifications (Email, SMS, Push, In-app)
- Notification templates and preferences
- Delivery tracking and retry logic
- Notification analytics

### **Admin Management**
- System overview and monitoring
- User and property moderation
- Bulk operations
- Recent activity tracking
- Pending approvals management

### **System Health**
- Comprehensive health monitoring
- Database connectivity checks
- Memory and disk usage monitoring
- Performance metrics

## Database Schema Features

### **Optimized for High-Volume Data**
- **Indexed query-heavy fields** (location, price, availability)
- **Efficient pagination** with proper OFFSET/LIMIT optimization
- **Full-text search** capabilities with GIN indexes
- **Spatial indexing** for location-based queries
- **Composite indexes** for common query patterns

### **Relational Integrity**
- **Foreign key constraints** maintaining data consistency
- **Cascade operations** for proper data cleanup
- **Check constraints** for data validation
- **Unique constraints** preventing duplicates

### **Performance Optimization**
- **84 strategic indexes** for query optimization
- **Partial indexes** for filtered queries
- **GIN indexes** for array and full-text operations
- **Composite indexes** for multi-column queries
- **Query optimization views** for monitoring

### **Scalability Preparation**
- **Connection pooling** configuration
- **Read replica** preparation
- **Sharding** considerations in schema design
- **Multi-tenancy** preparation
- **Automated cleanup** procedures

## Load Testing Data

### **12,000+ Mock Property Listings**
- Realistic property data for Port Harcourt
- Multiple property types and price ranges
- Geographic distribution across Port Harcourt areas
- Comprehensive amenities and features
- Realistic view and inquiry statistics

### **Sample Users**
- 50+ landlords with realistic profiles
- 20+ agents with performance data
- Admin and tenant accounts
- Verified and unverified users
- Active and inactive accounts

This comprehensive API provides a complete real estate platform backend with enterprise-grade features, security, and performance optimization.
