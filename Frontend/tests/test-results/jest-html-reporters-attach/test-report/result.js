window.jest_html_reporters_callback__({"numFailedTestSuites":4,"numFailedTests":0,"numPassedTestSuites":0,"numPassedTests":0,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":4,"numTodoTests":0,"numTotalTestSuites":4,"numTotalTests":0,"startTime":1752135195923,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"/Users/<USER>/Downloads/p-398841-1/src/tests/unit/services/validationService.test.ts","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    You appear to be using a native ECMAScript module configuration file, which is only supported when running Babel asynchronously or when using the Node.js `--experimental-require-module` flag.\n        at /Users/<USER>/Downloads/p-398841-1/babel.config.js\n\n      \u001b[2mat loadPartialConfigSync (\u001b[22mnode_modules/@babel/core/src/config/index.ts\u001b[2m:50:60)\u001b[22m\n      \u001b[2mat loadPartialConfigSync (\u001b[22mnode_modules/@babel/core/src/config/index.ts\u001b[2m:69:14)\u001b[22m\n      \u001b[2mat ScriptTransformer._getCacheKey (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:195:41)\u001b[22m\n      \u001b[2mat ScriptTransformer._getFileCachePath (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:231:27)\u001b[22m\n      \u001b[2mat ScriptTransformer.transformSource (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:402:32)\u001b[22m\n      \u001b[2mat ScriptTransformer._transformAndBuildScript (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:519:40)\u001b[22m\n      \u001b[2mat ScriptTransformer.transform (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:558:19)\u001b[22m\n","testResults":[]},{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"/Users/<USER>/Downloads/p-398841-1/src/tests/unit/hooks/useAuth.test.tsx","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    You appear to be using a native ECMAScript module configuration file, which is only supported when running Babel asynchronously or when using the Node.js `--experimental-require-module` flag.\n        at /Users/<USER>/Downloads/p-398841-1/babel.config.js\n\n      \u001b[2mat loadPartialConfigSync (\u001b[22mnode_modules/@babel/core/src/config/index.ts\u001b[2m:50:60)\u001b[22m\n      \u001b[2mat loadPartialConfigSync (\u001b[22mnode_modules/@babel/core/src/config/index.ts\u001b[2m:69:14)\u001b[22m\n      \u001b[2mat ScriptTransformer._getCacheKey (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:195:41)\u001b[22m\n      \u001b[2mat ScriptTransformer._getFileCachePath (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:231:27)\u001b[22m\n      \u001b[2mat ScriptTransformer.transformSource (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:402:32)\u001b[22m\n      \u001b[2mat ScriptTransformer._transformAndBuildScript (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:519:40)\u001b[22m\n      \u001b[2mat ScriptTransformer.transform (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:558:19)\u001b[22m\n","testResults":[]},{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"/Users/<USER>/Downloads/p-398841-1/src/tests/integration/api/properties.test.ts","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    You appear to be using a native ECMAScript module configuration file, which is only supported when running Babel asynchronously or when using the Node.js `--experimental-require-module` flag.\n        at /Users/<USER>/Downloads/p-398841-1/babel.config.js\n\n      \u001b[2mat loadPartialConfigSync (\u001b[22mnode_modules/@babel/core/src/config/index.ts\u001b[2m:50:60)\u001b[22m\n      \u001b[2mat loadPartialConfigSync (\u001b[22mnode_modules/@babel/core/src/config/index.ts\u001b[2m:69:14)\u001b[22m\n      \u001b[2mat ScriptTransformer._getCacheKey (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:195:41)\u001b[22m\n      \u001b[2mat ScriptTransformer._getFileCachePath (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:231:27)\u001b[22m\n      \u001b[2mat ScriptTransformer.transformSource (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:402:32)\u001b[22m\n      \u001b[2mat ScriptTransformer._transformAndBuildScript (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:519:40)\u001b[22m\n      \u001b[2mat ScriptTransformer.transform (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:558:19)\u001b[22m\n","testResults":[]},{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"/Users/<USER>/Downloads/p-398841-1/src/tests/unit/components/PropertyCard.test.tsx","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    You appear to be using a native ECMAScript module configuration file, which is only supported when running Babel asynchronously or when using the Node.js `--experimental-require-module` flag.\n        at /Users/<USER>/Downloads/p-398841-1/babel.config.js\n\n      \u001b[2mat loadPartialConfigSync (\u001b[22mnode_modules/@babel/core/src/config/index.ts\u001b[2m:50:60)\u001b[22m\n      \u001b[2mat loadPartialConfigSync (\u001b[22mnode_modules/@babel/core/src/config/index.ts\u001b[2m:69:14)\u001b[22m\n      \u001b[2mat ScriptTransformer._getCacheKey (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:195:41)\u001b[22m\n      \u001b[2mat ScriptTransformer._getFileCachePath (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:231:27)\u001b[22m\n      \u001b[2mat ScriptTransformer.transformSource (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:402:32)\u001b[22m\n      \u001b[2mat ScriptTransformer._transformAndBuildScript (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:519:40)\u001b[22m\n      \u001b[2mat ScriptTransformer.transform (\u001b[22mnode_modules/@jest/transform/build/index.js\u001b[2m:558:19)\u001b[22m\n","testResults":[]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["src/**/*.{ts,tsx}","!src/**/*.d.ts","!src/tests/**/*","!src/main.tsx","!src/vite-env.d.ts","!src/**/*.stories.{ts,tsx}","!src/**/*.config.{ts,tsx}"],"coverageDirectory":"/Users/<USER>/Downloads/p-398841-1/coverage","coverageProvider":"babel","coverageReporters":["text","lcov","html","json-summary","cobertura"],"coverageThreshold":{"global":{"branches":80,"functions":80,"lines":80,"statements":80},"./src/services/":{"branches":90,"functions":90,"lines":90,"statements":90},"./src/hooks/":{"branches":85,"functions":85,"lines":85,"statements":85}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[{"displayName":"unit","testMatch":["<rootDir>/src/tests/unit/**/*.test.{ts,tsx}"],"setupFilesAfterEnv":["<rootDir>/src/tests/setup/jest.setup.ts"],"rootDir":"/Users/<USER>/Downloads/p-398841-1"},{"displayName":"integration","testMatch":["<rootDir>/src/tests/integration/**/*.test.{ts,tsx}"],"setupFilesAfterEnv":["<rootDir>/src/tests/setup/jest.setup.ts"],"rootDir":"/Users/<USER>/Downloads/p-398841-1"}],"reporters":[["default",{}],["/Users/<USER>/Downloads/p-398841-1/node_modules/jest-junit/index.js",{"outputDirectory":"<rootDir>/test-results","outputName":"junit.xml","classNameTemplate":"{classname}","titleTemplate":"{title}","ancestorSeparator":" › ","usePathForSuiteName":true}],["/Users/<USER>/Downloads/p-398841-1/node_modules/jest-html-reporters/index.js",{"publicPath":"<rootDir>/test-results","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"PHCityRent Test Report"}]],"rootDir":"/Users/<USER>/Downloads/p-398841-1","runInBand":false,"runTestsByPath":false,"seed":-1557781893,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":[],"type":"TestPathPatterns"},"testResultsProcessor":"/Users/<USER>/Downloads/p-398841-1/src/tests/utils/testResultsProcessor.js","testSequencer":"/Users/<USER>/Downloads/p-398841-1/node_modules/@jest/test-sequencer/build/index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchPlugins":[{"config":{},"path":"/Users/<USER>/Downloads/p-398841-1/node_modules/jest-watch-typeahead/build/file_name_plugin/plugin.js"},{"config":{},"path":"/Users/<USER>/Downloads/p-398841-1/node_modules/jest-watch-typeahead/build/test_name_plugin/plugin.js"}],"watchman":true,"workerThreads":false,"coverageLinkPath":"../coverage/lcov-report/index.html"},"endTime":1752135196242,"_reporterOptions":{"publicPath":"/Users/<USER>/Downloads/p-398841-1/test-results","filename":"test-report.html","expand":true,"pageTitle":"PHCityRent Test Report","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})