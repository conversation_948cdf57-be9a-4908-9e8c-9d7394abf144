// =====================================================
// CSRF PROTECTION MIDDLEWARE
// Cross-Site Request Forgery protection with token validation
// =====================================================

import { supabase } from '@/lib/supabase';

// Types and Interfaces
export interface CSRFConfig {
  tokenLength: number;
  cookieName: string;
  headerName: string;
  sameSite: 'strict' | 'lax' | 'none';
  secure: boolean;
  httpOnly: boolean;
  maxAge: number; // in seconds
  ignoreMethods: string[];
  trustedOrigins: string[];
  enableDoubleSubmit: boolean;
  enableRefererValidation: boolean;
  enableCustomHeaderValidation: boolean;
}

export interface CSRFToken {
  token: string;
  sessionId: string;
  userId?: string;
  createdAt: number;
  expiresAt: number;
  used: boolean;
  ipAddress: string;
  userAgent: string;
}

export interface CSRFValidationResult {
  valid: boolean;
  reason?: string;
  token?: CSRFToken;
  securityFlags: string[];
}

export interface CSRFAttackLog {
  id: string;
  attackType: 'missing_token' | 'invalid_token' | 'expired_token' | 'origin_mismatch' | 'referer_mismatch';
  severity: 'low' | 'medium' | 'high' | 'critical';
  ipAddress: string;
  userAgent: string;
  referer?: string;
  origin?: string;
  timestamp: number;
  blocked: boolean;
}

class CSRFProtectionService {
  private config: CSRFConfig = {
    tokenLength: 32,
    cookieName: 'csrf-token',
    headerName: 'X-CSRF-Token',
    sameSite: 'strict',
    secure: true,
    httpOnly: false, // Must be false to allow JavaScript access
    maxAge: 3600, // 1 hour
    ignoreMethods: ['GET', 'HEAD', 'OPTIONS'],
    trustedOrigins: [
      'https://phcityrent.com',
      'https://www.phcityrent.com',
      'http://localhost:3000', // Development
      'http://localhost:5173'  // Vite dev server
    ],
    enableDoubleSubmit: true,
    enableRefererValidation: true,
    enableCustomHeaderValidation: true
  };

  private tokenStore: Map<string, CSRFToken> = new Map();
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startCleanupInterval();
  }

  // Generate CSRF token
  generateToken(sessionId: string, userId?: string, ipAddress?: string, userAgent?: string): CSRFToken {
    const token = this.generateRandomToken(this.config.tokenLength);
    const now = Date.now();
    
    const csrfToken: CSRFToken = {
      token,
      sessionId,
      userId,
      createdAt: now,
      expiresAt: now + (this.config.maxAge * 1000),
      used: false,
      ipAddress: ipAddress || '127.0.0.1',
      userAgent: userAgent || 'unknown'
    };

    this.tokenStore.set(token, csrfToken);
    return csrfToken;
  }

  // Validate CSRF token
  async validateToken(req: any): Promise<CSRFValidationResult> {
    try {
      const method = req.method?.toUpperCase();
      const securityFlags: string[] = [];

      // Skip validation for safe methods
      if (this.config.ignoreMethods.includes(method)) {
        return { valid: true, securityFlags };
      }

      // Get token from header or body
      const headerToken = req.headers?.[this.config.headerName.toLowerCase()];
      const bodyToken = req.body?.[this.config.cookieName];
      const cookieToken = req.cookies?.[this.config.cookieName];

      let submittedToken = headerToken || bodyToken;

      // Double submit cookie validation
      if (this.config.enableDoubleSubmit) {
        if (!cookieToken) {
          await this.logCSRFAttack({
            attackType: 'missing_token',
            severity: 'high',
            ipAddress: this.getClientIP(req),
            userAgent: req.headers?.['user-agent'] || 'unknown',
            timestamp: Date.now(),
            blocked: true
          });

          return {
            valid: false,
            reason: 'CSRF cookie missing',
            securityFlags: ['missing_csrf_cookie']
          };
        }

        if (submittedToken !== cookieToken) {
          await this.logCSRFAttack({
            attackType: 'invalid_token',
            severity: 'critical',
            ipAddress: this.getClientIP(req),
            userAgent: req.headers?.['user-agent'] || 'unknown',
            timestamp: Date.now(),
            blocked: true
          });

          return {
            valid: false,
            reason: 'CSRF token mismatch',
            securityFlags: ['csrf_token_mismatch']
          };
        }

        submittedToken = cookieToken;
      }

      if (!submittedToken) {
        await this.logCSRFAttack({
          attackType: 'missing_token',
          severity: 'high',
          ipAddress: this.getClientIP(req),
          userAgent: req.headers?.['user-agent'] || 'unknown',
          timestamp: Date.now(),
          blocked: true
        });

        return {
          valid: false,
          reason: 'CSRF token missing',
          securityFlags: ['missing_csrf_token']
        };
      }

      // Validate token exists and is not expired
      const tokenData = this.tokenStore.get(submittedToken);
      if (!tokenData) {
        await this.logCSRFAttack({
          attackType: 'invalid_token',
          severity: 'high',
          ipAddress: this.getClientIP(req),
          userAgent: req.headers?.['user-agent'] || 'unknown',
          timestamp: Date.now(),
          blocked: true
        });

        return {
          valid: false,
          reason: 'Invalid CSRF token',
          securityFlags: ['invalid_csrf_token']
        };
      }

      // Check if token is expired
      if (Date.now() > tokenData.expiresAt) {
        this.tokenStore.delete(submittedToken);
        
        await this.logCSRFAttack({
          attackType: 'expired_token',
          severity: 'medium',
          ipAddress: this.getClientIP(req),
          userAgent: req.headers?.['user-agent'] || 'unknown',
          timestamp: Date.now(),
          blocked: true
        });

        return {
          valid: false,
          reason: 'CSRF token expired',
          securityFlags: ['expired_csrf_token']
        };
      }

      // Validate origin if enabled
      if (this.config.enableRefererValidation) {
        const originValid = await this.validateOrigin(req, securityFlags);
        if (!originValid) {
          await this.logCSRFAttack({
            attackType: 'origin_mismatch',
            severity: 'critical',
            ipAddress: this.getClientIP(req),
            userAgent: req.headers?.['user-agent'] || 'unknown',
            origin: req.headers?.origin,
            referer: req.headers?.referer,
            timestamp: Date.now(),
            blocked: true
          });

          return {
            valid: false,
            reason: 'Origin validation failed',
            securityFlags
          };
        }
      }

      // Validate custom headers if enabled
      if (this.config.enableCustomHeaderValidation) {
        const customHeaderValid = this.validateCustomHeaders(req, securityFlags);
        if (!customHeaderValid) {
          return {
            valid: false,
            reason: 'Custom header validation failed',
            securityFlags
          };
        }
      }

      // Additional security checks
      await this.performAdditionalSecurityChecks(req, tokenData, securityFlags);

      // Mark token as used (optional - for one-time use tokens)
      // tokenData.used = true;

      return {
        valid: true,
        token: tokenData,
        securityFlags
      };
    } catch (error) {
      console.error('CSRF validation error:', error);
      return {
        valid: false,
        reason: 'CSRF validation failed',
        securityFlags: ['validation_error']
      };
    }
  }

  // Validate origin and referer
  private async validateOrigin(req: any, securityFlags: string[]): Promise<boolean> {
    const origin = req.headers?.origin;
    const referer = req.headers?.referer;
    const host = req.headers?.host;

    // Check origin header
    if (origin) {
      if (!this.config.trustedOrigins.includes(origin)) {
        securityFlags.push('untrusted_origin');
        return false;
      }
    }

    // Check referer header
    if (referer) {
      try {
        const refererUrl = new URL(referer);
        const refererOrigin = `${refererUrl.protocol}//${refererUrl.host}`;
        
        if (!this.config.trustedOrigins.includes(refererOrigin)) {
          securityFlags.push('untrusted_referer');
          return false;
        }
      } catch (error) {
        securityFlags.push('invalid_referer_format');
        return false;
      }
    }

    // If neither origin nor referer is present, it might be suspicious
    if (!origin && !referer) {
      securityFlags.push('missing_origin_and_referer');
      // Don't fail validation, but flag as suspicious
    }

    return true;
  }

  // Validate custom headers
  private validateCustomHeaders(req: any, securityFlags: string[]): boolean {
    // Check for X-Requested-With header (common AJAX protection)
    const requestedWith = req.headers?.['x-requested-with'];
    if (!requestedWith || requestedWith !== 'XMLHttpRequest') {
      securityFlags.push('missing_ajax_header');
      // Don't fail validation for this, as it's not always required
    }

    // Check for custom application header
    const appHeader = req.headers?.['x-phcityrent-app'];
    if (!appHeader) {
      securityFlags.push('missing_app_header');
      // Don't fail validation, but flag as suspicious
    }

    return true;
  }

  // Perform additional security checks
  private async performAdditionalSecurityChecks(
    req: any, 
    tokenData: CSRFToken, 
    securityFlags: string[]
  ): Promise<void> {
    const currentIP = this.getClientIP(req);
    const currentUserAgent = req.headers?.['user-agent'] || 'unknown';

    // Check if IP address changed (might indicate token theft)
    if (tokenData.ipAddress !== currentIP) {
      securityFlags.push('ip_address_changed');
    }

    // Check if user agent changed significantly
    if (tokenData.userAgent !== currentUserAgent) {
      securityFlags.push('user_agent_changed');
    }

    // Check for rapid token usage (might indicate automated attacks)
    const timeSinceCreation = Date.now() - tokenData.createdAt;
    if (timeSinceCreation < 1000) { // Less than 1 second
      securityFlags.push('rapid_token_usage');
    }
  }

  // Set CSRF cookie
  setCookie(res: any, token: string): void {
    const cookieOptions = {
      httpOnly: this.config.httpOnly,
      secure: this.config.secure,
      sameSite: this.config.sameSite,
      maxAge: this.config.maxAge * 1000, // Convert to milliseconds
      path: '/'
    };

    res.cookie(this.config.cookieName, token, cookieOptions);
  }

  // Generate and set CSRF protection
  async setupCSRFProtection(req: any, res: any): Promise<string> {
    const sessionId = req.sessionID || this.generateSessionId();
    const userId = req.user?.id;
    const ipAddress = this.getClientIP(req);
    const userAgent = req.headers?.['user-agent'];

    const tokenData = this.generateToken(sessionId, userId, ipAddress, userAgent);
    this.setCookie(res, tokenData.token);

    // Also send token in response header for SPA applications
    res.setHeader(this.config.headerName, tokenData.token);

    return tokenData.token;
  }

  // Log CSRF attack
  private async logCSRFAttack(attack: Omit<CSRFAttackLog, 'id'>): Promise<void> {
    try {
      const attackLog: CSRFAttackLog = {
        id: self.crypto.randomUUID(),
        ...attack
      };

      await supabase
        .from('csrf_attack_logs')
        .insert({
          attack_id: attackLog.id,
          attack_type: attackLog.attackType,
          severity: attackLog.severity,
          ip_address: attackLog.ipAddress,
          user_agent: attackLog.userAgent,
          referer: attackLog.referer,
          origin: attackLog.origin,
          blocked: attackLog.blocked,
          timestamp: new Date(attackLog.timestamp).toISOString()
        });
    } catch (error) {
      console.error('Failed to log CSRF attack:', error);
    }
  }

  // Utility functions
  private getClientIP(req: any): string {
    return req.ip || 
           req.connection?.remoteAddress || 
           req.socket?.remoteAddress || 
           req.headers?.['x-forwarded-for']?.split(',')[0] || 
           '127.0.0.1';
  }

  private generateSessionId(): string {
    return this.generateRandomToken(16);
  }

  // Generate random token using Web Crypto API
  private generateRandomToken(length: number): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  // Start cleanup interval for expired tokens
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredTokens();
    }, 5 * 60 * 1000); // Cleanup every 5 minutes
  }

  // Cleanup expired tokens
  private cleanupExpiredTokens(): void {
    const now = Date.now();
    const expiredTokens: string[] = [];

    for (const [token, tokenData] of this.tokenStore.entries()) {
      if (now > tokenData.expiresAt) {
        expiredTokens.push(token);
      }
    }

    expiredTokens.forEach(token => this.tokenStore.delete(token));
    
    if (expiredTokens.length > 0) {
      console.log(`Cleaned up ${expiredTokens.length} expired CSRF tokens`);
    }
  }

  // Get CSRF statistics
  async getCSRFStats(timeRange: 'hour' | 'day' | 'week' = 'day'): Promise<any> {
    try {
      const hours = timeRange === 'hour' ? 1 : timeRange === 'day' ? 24 : 168;
      const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

      const { data: attacks, error } = await supabase
        .from('csrf_attack_logs')
        .select('*')
        .gte('timestamp', startTime.toISOString());

      if (error) throw error;

      const stats = {
        totalAttacks: attacks?.length || 0,
        attacksByType: {},
        attacksBySeverity: {},
        topAttackerIPs: {},
        blockedAttacks: 0,
        timeDistribution: {}
      };

      attacks?.forEach(attack => {
        // By type
        stats.attacksByType[attack.attack_type] = 
          (stats.attacksByType[attack.attack_type] || 0) + 1;

        // By severity
        stats.attacksBySeverity[attack.severity] = 
          (stats.attacksBySeverity[attack.severity] || 0) + 1;

        // Top attacker IPs
        stats.topAttackerIPs[attack.ip_address] = 
          (stats.topAttackerIPs[attack.ip_address] || 0) + 1;

        // Blocked attacks
        if (attack.blocked) {
          stats.blockedAttacks++;
        }

        // Time distribution
        const hour = new Date(attack.timestamp).getHours();
        stats.timeDistribution[hour] = (stats.timeDistribution[hour] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Failed to get CSRF stats:', error);
      return null;
    }
  }

  // Update configuration
  updateConfig(newConfig: Partial<CSRFConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Get current configuration
  getConfig(): CSRFConfig {
    return { ...this.config };
  }

  // Revoke token
  revokeToken(token: string): boolean {
    return this.tokenStore.delete(token);
  }

  // Revoke all tokens for session
  revokeSessionTokens(sessionId: string): number {
    let revokedCount = 0;
    
    for (const [token, tokenData] of this.tokenStore.entries()) {
      if (tokenData.sessionId === sessionId) {
        this.tokenStore.delete(token);
        revokedCount++;
      }
    }
    
    return revokedCount;
  }

  // Get active token count
  getActiveTokenCount(): number {
    return this.tokenStore.size;
  }

  // Cleanup on shutdown
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.tokenStore.clear();
  }
}

// Export singleton instance
export const csrfProtectionService = new CSRFProtectionService();
export default csrfProtectionService;
