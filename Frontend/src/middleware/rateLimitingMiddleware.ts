// =====================================================
// RATE LIMITING MIDDLEWARE
// Advanced rate limiting with adaptive algorithms
// =====================================================

import { supabase } from '@/lib/supabase';

// Types and Interfaces
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator: (req: any) => string; // Function to generate rate limit key
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
  enableAdaptive: boolean; // Enable adaptive rate limiting
  enableBurst: boolean; // Enable burst protection
  burstMultiplier: number; // Burst allowance multiplier
}

export interface RateLimitRule {
  id: string;
  name: string;
  pattern: string; // URL pattern or endpoint
  config: RateLimitConfig;
  priority: number;
  isActive: boolean;
  userTypes?: string[]; // Apply to specific user types
  ipWhitelist?: string[];
  ipBlacklist?: string[];
}

export interface RateLimitEntry {
  key: string;
  count: number;
  resetTime: number;
  firstRequest: number;
  lastRequest: number;
  blocked: boolean;
  adaptiveMultiplier: number;
}

export interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
  reason?: string;
}

export interface RateLimitAlert {
  type: 'threshold_exceeded' | 'burst_detected' | 'suspicious_activity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  key: string;
  requestCount: number;
  timeWindow: number;
  timestamp: number;
}

class RateLimitingService {
  private rateLimitStore: Map<string, RateLimitEntry> = new Map();
  private rules: RateLimitRule[] = [];
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeDefaultRules();
    this.startCleanupInterval();
  }

  // Initialize default rate limiting rules
  private initializeDefaultRules(): void {
    this.rules = [
      // Authentication endpoints
      {
        id: 'auth_login',
        name: 'Login Rate Limit',
        pattern: '/api/auth/login',
        config: {
          windowMs: 15 * 60 * 1000, // 15 minutes
          maxRequests: 5,
          keyGenerator: (req) => `login:${this.getClientIP(req)}`,
          skipSuccessfulRequests: false,
          skipFailedRequests: false,
          enableAdaptive: true,
          enableBurst: false,
          burstMultiplier: 1.5
        },
        priority: 1,
        isActive: true
      },

      // Password reset
      {
        id: 'auth_password_reset',
        name: 'Password Reset Rate Limit',
        pattern: '/api/auth/reset-password',
        config: {
          windowMs: 60 * 60 * 1000, // 1 hour
          maxRequests: 3,
          keyGenerator: (req) => `reset:${this.getClientIP(req)}`,
          skipSuccessfulRequests: false,
          skipFailedRequests: false,
          enableAdaptive: false,
          enableBurst: false,
          burstMultiplier: 1
        },
        priority: 1,
        isActive: true
      },

      // API endpoints - general
      {
        id: 'api_general',
        name: 'General API Rate Limit',
        pattern: '/api/*',
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 100,
          keyGenerator: (req) => `api:${this.getUserId(req) || this.getClientIP(req)}`,
          skipSuccessfulRequests: false,
          skipFailedRequests: true,
          enableAdaptive: true,
          enableBurst: true,
          burstMultiplier: 2
        },
        priority: 3,
        isActive: true
      },

      // Property search
      {
        id: 'property_search',
        name: 'Property Search Rate Limit',
        pattern: '/api/properties/search',
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 30,
          keyGenerator: (req) => `search:${this.getUserId(req) || this.getClientIP(req)}`,
          skipSuccessfulRequests: false,
          skipFailedRequests: true,
          enableAdaptive: true,
          enableBurst: true,
          burstMultiplier: 1.5
        },
        priority: 2,
        isActive: true
      },

      // File uploads
      {
        id: 'file_upload',
        name: 'File Upload Rate Limit',
        pattern: '/api/upload/*',
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 10,
          keyGenerator: (req) => `upload:${this.getUserId(req) || this.getClientIP(req)}`,
          skipSuccessfulRequests: false,
          skipFailedRequests: false,
          enableAdaptive: false,
          enableBurst: false,
          burstMultiplier: 1
        },
        priority: 1,
        isActive: true
      },

      // Message sending
      {
        id: 'message_send',
        name: 'Message Sending Rate Limit',
        pattern: '/api/messages',
        config: {
          windowMs: 60 * 1000, // 1 minute
          maxRequests: 20,
          keyGenerator: (req) => `message:${this.getUserId(req)}`,
          skipSuccessfulRequests: false,
          skipFailedRequests: true,
          enableAdaptive: true,
          enableBurst: true,
          burstMultiplier: 1.3
        },
        priority: 2,
        isActive: true,
        userTypes: ['tenant', 'landlord', 'agent']
      }
    ];
  }

  // Check rate limit for request
  async checkRateLimit(req: any): Promise<RateLimitResult> {
    try {
      const rule = this.findMatchingRule(req);
      if (!rule) {
        return {
          allowed: true,
          limit: Infinity,
          remaining: Infinity,
          resetTime: Date.now()
        };
      }

      const key = rule.config.keyGenerator(req);
      const now = Date.now();
      
      // Get or create rate limit entry
      let entry = this.rateLimitStore.get(key);
      if (!entry) {
        entry = {
          key,
          count: 0,
          resetTime: now + rule.config.windowMs,
          firstRequest: now,
          lastRequest: now,
          blocked: false,
          adaptiveMultiplier: 1
        };
        this.rateLimitStore.set(key, entry);
      }

      // Reset window if expired
      if (now >= entry.resetTime) {
        entry.count = 0;
        entry.resetTime = now + rule.config.windowMs;
        entry.firstRequest = now;
        entry.blocked = false;
        
        // Adjust adaptive multiplier based on previous behavior
        if (rule.config.enableAdaptive) {
          this.adjustAdaptiveMultiplier(entry, rule);
        }
      }

      // Calculate effective limit
      let effectiveLimit = rule.config.maxRequests;
      if (rule.config.enableAdaptive) {
        effectiveLimit = Math.floor(effectiveLimit * entry.adaptiveMultiplier);
      }

      // Check burst protection
      if (rule.config.enableBurst) {
        const burstLimit = Math.floor(effectiveLimit * rule.config.burstMultiplier);
        const timeSinceFirst = now - entry.firstRequest;
        const expectedRequests = (timeSinceFirst / rule.config.windowMs) * effectiveLimit;
        
        if (entry.count > expectedRequests + (burstLimit - effectiveLimit)) {
          await this.logRateLimitViolation(key, rule, entry, 'burst_detected');
          return {
            allowed: false,
            limit: effectiveLimit,
            remaining: 0,
            resetTime: entry.resetTime,
            retryAfter: Math.ceil((entry.resetTime - now) / 1000),
            reason: 'Burst limit exceeded'
          };
        }
      }

      // Increment counter
      entry.count++;
      entry.lastRequest = now;

      // Check if limit exceeded
      if (entry.count > effectiveLimit) {
        entry.blocked = true;
        await this.logRateLimitViolation(key, rule, entry, 'threshold_exceeded');
        
        return {
          allowed: false,
          limit: effectiveLimit,
          remaining: 0,
          resetTime: entry.resetTime,
          retryAfter: Math.ceil((entry.resetTime - now) / 1000),
          reason: 'Rate limit exceeded'
        };
      }

      // Check for suspicious activity
      await this.checkSuspiciousActivity(key, rule, entry);

      return {
        allowed: true,
        limit: effectiveLimit,
        remaining: effectiveLimit - entry.count,
        resetTime: entry.resetTime
      };
    } catch (error) {
      console.error('Rate limit check error:', error);
      // Fail open - allow request if rate limiting fails
      return {
        allowed: true,
        limit: 0,
        remaining: 0,
        resetTime: Date.now()
      };
    }
  }

  // Find matching rule for request
  private findMatchingRule(req: any): RateLimitRule | null {
    const url = req.url || req.path || '';
    const userType = this.getUserType(req);
    const clientIP = this.getClientIP(req);

    // Sort rules by priority (lower number = higher priority)
    const sortedRules = this.rules
      .filter(rule => rule.isActive)
      .sort((a, b) => a.priority - b.priority);

    for (const rule of sortedRules) {
      // Check URL pattern match
      const pattern = rule.pattern.replace(/\*/g, '.*');
      const regex = new RegExp(`^${pattern}$`);
      
      if (!regex.test(url)) continue;

      // Check user type restriction
      if (rule.userTypes && userType && !rule.userTypes.includes(userType)) {
        continue;
      }

      // Check IP whitelist
      if (rule.ipWhitelist && rule.ipWhitelist.length > 0) {
        if (!rule.ipWhitelist.includes(clientIP)) continue;
      }

      // Check IP blacklist
      if (rule.ipBlacklist && rule.ipBlacklist.includes(clientIP)) {
        continue;
      }

      return rule;
    }

    return null;
  }

  // Adjust adaptive multiplier based on behavior
  private adjustAdaptiveMultiplier(entry: RateLimitEntry, rule: RateLimitRule): void {
    const timeSinceFirst = entry.lastRequest - entry.firstRequest;
    const windowDuration = rule.config.windowMs;
    
    // If user consistently uses less than 50% of limit, increase their allowance
    if (entry.count < rule.config.maxRequests * 0.5 && timeSinceFirst > windowDuration * 0.8) {
      entry.adaptiveMultiplier = Math.min(entry.adaptiveMultiplier * 1.1, 2.0);
    }
    // If user consistently hits the limit, decrease their allowance
    else if (entry.count >= rule.config.maxRequests * 0.9) {
      entry.adaptiveMultiplier = Math.max(entry.adaptiveMultiplier * 0.9, 0.5);
    }
  }

  // Check for suspicious activity patterns
  private async checkSuspiciousActivity(key: string, rule: RateLimitRule, entry: RateLimitEntry): Promise<void> {
    const now = Date.now();
    const timeSinceFirst = now - entry.firstRequest;
    
    // Check for rapid-fire requests (too many requests in too short a time)
    if (entry.count > rule.config.maxRequests * 0.8 && timeSinceFirst < rule.config.windowMs * 0.2) {
      await this.createAlert({
        type: 'suspicious_activity',
        severity: 'high',
        key,
        requestCount: entry.count,
        timeWindow: timeSinceFirst,
        timestamp: now
      });
    }

    // Check for consistent limit testing
    if (entry.count === rule.config.maxRequests) {
      await this.createAlert({
        type: 'suspicious_activity',
        severity: 'medium',
        key,
        requestCount: entry.count,
        timeWindow: timeSinceFirst,
        timestamp: now
      });
    }
  }

  // Log rate limit violation
  private async logRateLimitViolation(
    key: string, 
    rule: RateLimitRule, 
    entry: RateLimitEntry, 
    violationType: string
  ): Promise<void> {
    try {
      await supabase
        .from('rate_limit_violations')
        .insert({
          rate_limit_key: key,
          rule_id: rule.id,
          rule_name: rule.name,
          violation_type: violationType,
          request_count: entry.count,
          limit_value: rule.config.maxRequests,
          window_ms: rule.config.windowMs,
          user_id: this.extractUserIdFromKey(key),
          ip_address: this.extractIPFromKey(key),
          timestamp: new Date().toISOString()
        });
    } catch (error) {
      console.error('Failed to log rate limit violation:', error);
    }
  }

  // Create rate limit alert
  private async createAlert(alert: RateLimitAlert): Promise<void> {
    try {
      await supabase
        .from('rate_limit_alerts')
        .insert({
          alert_type: alert.type,
          severity: alert.severity,
          rate_limit_key: alert.key,
          request_count: alert.requestCount,
          time_window: alert.timeWindow,
          user_id: this.extractUserIdFromKey(alert.key),
          ip_address: this.extractIPFromKey(alert.key),
          timestamp: new Date(alert.timestamp).toISOString()
        });
    } catch (error) {
      console.error('Failed to create rate limit alert:', error);
    }
  }

  // Utility functions
  private getClientIP(req: any): string {
    return req.ip || 
           req.connection?.remoteAddress || 
           req.socket?.remoteAddress || 
           req.headers?.['x-forwarded-for']?.split(',')[0] || 
           '127.0.0.1';
  }

  private getUserId(req: any): string | null {
    return req.user?.id || req.headers?.['x-user-id'] || null;
  }

  private getUserType(req: any): string | null {
    return req.user?.role || req.headers?.['x-user-type'] || null;
  }

  private extractUserIdFromKey(key: string): string | null {
    const parts = key.split(':');
    if (parts.length > 1 && parts[1].length === 36) { // UUID length
      return parts[1];
    }
    return null;
  }

  private extractIPFromKey(key: string): string | null {
    const parts = key.split(':');
    const lastPart = parts[parts.length - 1];
    // Simple IP pattern check
    if (/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(lastPart)) {
      return lastPart;
    }
    return null;
  }

  // Start cleanup interval
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredEntries();
    }, 5 * 60 * 1000); // Cleanup every 5 minutes
  }

  // Cleanup expired entries
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.rateLimitStore.entries()) {
      if (now >= entry.resetTime + 60000) { // 1 minute grace period
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.rateLimitStore.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`Cleaned up ${expiredKeys.length} expired rate limit entries`);
    }
  }

  // Add new rule
  addRule(rule: RateLimitRule): void {
    this.rules.push(rule);
    this.rules.sort((a, b) => a.priority - b.priority);
  }

  // Update existing rule
  updateRule(ruleId: string, updates: Partial<RateLimitRule>): boolean {
    const index = this.rules.findIndex(rule => rule.id === ruleId);
    if (index === -1) return false;

    this.rules[index] = { ...this.rules[index], ...updates };
    this.rules.sort((a, b) => a.priority - b.priority);
    return true;
  }

  // Remove rule
  removeRule(ruleId: string): boolean {
    const index = this.rules.findIndex(rule => rule.id === ruleId);
    if (index === -1) return false;

    this.rules.splice(index, 1);
    return true;
  }

  // Get all rules
  getRules(): RateLimitRule[] {
    return [...this.rules];
  }

  // Get rate limit statistics
  async getRateLimitStats(timeRange: 'hour' | 'day' | 'week' = 'day'): Promise<any> {
    try {
      const hours = timeRange === 'hour' ? 1 : timeRange === 'day' ? 24 : 168;
      const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

      const { data: violations, error } = await supabase
        .from('rate_limit_violations')
        .select('*')
        .gte('timestamp', startTime.toISOString());

      if (error) throw error;

      const stats = {
        totalViolations: violations?.length || 0,
        violationsByType: {},
        violationsByRule: {},
        topOffenders: {},
        timeDistribution: {}
      };

      violations?.forEach(violation => {
        // By type
        stats.violationsByType[violation.violation_type] = 
          (stats.violationsByType[violation.violation_type] || 0) + 1;

        // By rule
        stats.violationsByRule[violation.rule_name] = 
          (stats.violationsByRule[violation.rule_name] || 0) + 1;

        // Top offenders
        const key = violation.user_id || violation.ip_address;
        stats.topOffenders[key] = (stats.topOffenders[key] || 0) + 1;

        // Time distribution
        const hour = new Date(violation.timestamp).getHours();
        stats.timeDistribution[hour] = (stats.timeDistribution[hour] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Failed to get rate limit stats:', error);
      return null;
    }
  }

  // Reset rate limit for specific key
  resetRateLimit(key: string): boolean {
    return this.rateLimitStore.delete(key);
  }

  // Get current rate limit status for key
  getRateLimitStatus(key: string): RateLimitEntry | null {
    return this.rateLimitStore.get(key) || null;
  }

  // Cleanup on shutdown
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.rateLimitStore.clear();
  }
}

// Export singleton instance
export const rateLimitingService = new RateLimitingService();
export default rateLimitingService;
