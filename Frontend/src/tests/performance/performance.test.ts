import { test, expect } from '@playwright/test';

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  LCP: 2500, // Largest Contentful Paint
  FID: 100,  // First Input Delay
  CLS: 0.1,  // Cumulative Layout Shift
  FCP: 1800, // First Contentful Paint
  TTFB: 800, // Time to First Byte
};

// Bundle size thresholds (in KB)
const BUNDLE_THRESHOLDS = {
  TOTAL_JS: 1000,
  TOTAL_CSS: 200,
  MAIN_CHUNK: 500,
  VENDOR_CHUNK: 800,
};

test.describe('Performance Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Enable performance monitoring
    await page.addInitScript(() => {
      window.performanceMetrics = [];
      
      // Collect Web Vitals
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          window.performanceMetrics.push({
            name: entry.name,
            value: entry.value,
            startTime: entry.startTime,
            duration: entry.duration,
          });
        }
      }).observe({ entryTypes: ['measure', 'navigation', 'resource'] });
    });
  });

  test('Homepage loads within performance thresholds', async ({ page }) => {
    const startTime = Date.now();
    
    // Navigate to homepage
    await page.goto('/');
    
    // Wait for page to be fully loaded
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Check basic load time
    expect(loadTime).toBeLessThan(3000);
    
    // Check for critical elements
    await expect(page.locator('nav')).toBeVisible();
    await expect(page.locator('main')).toBeVisible();
    
    // Measure Web Vitals
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        // Wait for metrics to be collected
        setTimeout(() => {
          resolve(window.performanceMetrics || []);
        }, 1000);
      });
    });
    
    console.log('Performance metrics:', metrics);
  });

  test('Properties page lazy loads efficiently', async ({ page }) => {
    await page.goto('/properties');
    
    // Check initial load
    await page.waitForSelector('[data-testid="property-card"]', { timeout: 5000 });
    
    // Count initially loaded properties
    const initialCount = await page.locator('[data-testid="property-card"]').count();
    expect(initialCount).toBeGreaterThan(0);
    expect(initialCount).toBeLessThanOrEqual(12); // Should not load all at once
    
    // Test infinite scroll/pagination
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    
    // Wait for more properties to load
    await page.waitForTimeout(1000);
    
    const finalCount = await page.locator('[data-testid="property-card"]').count();
    expect(finalCount).toBeGreaterThanOrEqual(initialCount);
  });

  test('Images load with lazy loading', async ({ page }) => {
    await page.goto('/properties');
    
    // Check that images have loading="lazy" attribute
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < Math.min(imageCount, 10); i++) {
      const img = images.nth(i);
      const loading = await img.getAttribute('loading');
      
      // Critical images might not have lazy loading
      if (loading) {
        expect(loading).toBe('lazy');
      }
    }
    
    // Check that images below the fold are not loaded initially
    const belowFoldImages = page.locator('img').nth(10); // Assuming 10+ images
    if (await belowFoldImages.count() > 0) {
      const src = await belowFoldImages.getAttribute('src');
      // Should be placeholder or data URL initially
      expect(src).toMatch(/(placeholder|data:image)/);
    }
  });

  test('Bundle size is within limits', async ({ page }) => {
    // Navigate to page and collect resource timing
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const resources = await page.evaluate(() => {
      return performance.getEntriesByType('resource').map(entry => ({
        name: entry.name,
        size: entry.transferSize || 0,
        duration: entry.duration,
        type: entry.initiatorType,
      }));
    });
    
    // Analyze JavaScript bundles
    const jsResources = resources.filter(r => 
      r.name.includes('.js') && r.type === 'script'
    );
    
    const totalJsSize = jsResources.reduce((sum, r) => sum + r.size, 0) / 1024; // KB
    expect(totalJsSize).toBeLessThan(BUNDLE_THRESHOLDS.TOTAL_JS);
    
    // Analyze CSS bundles
    const cssResources = resources.filter(r => 
      r.name.includes('.css') && r.type === 'link'
    );
    
    const totalCssSize = cssResources.reduce((sum, r) => sum + r.size, 0) / 1024; // KB
    expect(totalCssSize).toBeLessThan(BUNDLE_THRESHOLDS.TOTAL_CSS);
    
    console.log(`Total JS size: ${totalJsSize.toFixed(2)}KB`);
    console.log(`Total CSS size: ${totalCssSize.toFixed(2)}KB`);
  });

  test('Code splitting works correctly', async ({ page }) => {
    await page.goto('/');
    
    // Get initial chunks loaded
    const initialChunks = await page.evaluate(() => {
      return Array.from(document.querySelectorAll('script[src]')).map(
        script => script.src
      );
    });
    
    // Navigate to a different route
    await page.click('a[href="/properties"]');
    await page.waitForLoadState('networkidle');
    
    // Get chunks after navigation
    const afterNavigationChunks = await page.evaluate(() => {
      return Array.from(document.querySelectorAll('script[src]')).map(
        script => script.src
      );
    });
    
    // Should have loaded additional chunks
    expect(afterNavigationChunks.length).toBeGreaterThanOrEqual(initialChunks.length);
    
    // Check for route-specific chunks
    const newChunks = afterNavigationChunks.filter(
      chunk => !initialChunks.includes(chunk)
    );
    
    expect(newChunks.length).toBeGreaterThan(0);
    console.log('New chunks loaded:', newChunks);
  });

  test('Search functionality is performant', async ({ page }) => {
    await page.goto('/search');
    
    const searchInput = page.locator('input[placeholder*="search" i]').first();
    await searchInput.waitFor();
    
    // Measure search performance
    const startTime = Date.now();
    
    await searchInput.fill('apartment');
    
    // Wait for debounced search
    await page.waitForTimeout(500);
    
    // Wait for results
    await page.waitForSelector('[data-testid="search-results"]', { timeout: 3000 });
    
    const searchTime = Date.now() - startTime;
    expect(searchTime).toBeLessThan(2000); // Search should complete within 2 seconds
    
    // Check that results are displayed
    const results = page.locator('[data-testid="property-card"]');
    await expect(results.first()).toBeVisible();
  });

  test('Dashboard loads efficiently with skeleton loaders', async ({ page }) => {
    await page.goto('/agent-dashboard');
    
    // Check for skeleton loaders initially
    const skeletons = page.locator('[data-testid="skeleton"]');
    const skeletonCount = await skeletons.count();
    
    if (skeletonCount > 0) {
      // Skeletons should be visible initially
      await expect(skeletons.first()).toBeVisible();
      
      // Wait for actual content to load
      await page.waitForTimeout(2000);
      
      // Skeletons should be replaced with actual content
      const remainingSkeletons = await page.locator('[data-testid="skeleton"]').count();
      expect(remainingSkeletons).toBeLessThan(skeletonCount);
    }
    
    // Check that dashboard content is loaded
    await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible();
  });

  test('Mobile performance is acceptable', async ({ page, browserName }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    const startTime = Date.now();
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    // Mobile should load within 4 seconds (more lenient than desktop)
    expect(loadTime).toBeLessThan(4000);
    
    // Check mobile-specific optimizations
    const images = page.locator('img');
    const imageCount = await images.count();
    
    // Check that images are appropriately sized for mobile
    for (let i = 0; i < Math.min(imageCount, 5); i++) {
      const img = images.nth(i);
      const box = await img.boundingBox();
      
      if (box) {
        // Images should not exceed viewport width
        expect(box.width).toBeLessThanOrEqual(375);
      }
    }
  });

  test('Memory usage stays within bounds', async ({ page }) => {
    await page.goto('/');
    
    // Get initial memory usage
    const initialMemory = await page.evaluate(() => {
      return (performance as any).memory ? {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
      } : null;
    });
    
    if (initialMemory) {
      // Navigate through several pages to test for memory leaks
      const routes = ['/properties', '/agents', '/search', '/contact'];
      
      for (const route of routes) {
        await page.goto(route);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(1000);
      }
      
      // Return to homepage
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const finalMemory = await page.evaluate(() => {
        return {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        };
      });
      
      // Memory usage should not increase dramatically
      const memoryIncrease = finalMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
      const memoryIncreasePercent = (memoryIncrease / initialMemory.usedJSHeapSize) * 100;
      
      expect(memoryIncreasePercent).toBeLessThan(50); // Less than 50% increase
      
      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB (${memoryIncreasePercent.toFixed(2)}%)`);
    }
  });

  test('Third-party scripts load asynchronously', async ({ page }) => {
    await page.goto('/');
    
    // Check that third-party scripts have async or defer attributes
    const scripts = await page.evaluate(() => {
      return Array.from(document.querySelectorAll('script[src]')).map(script => ({
        src: script.src,
        async: script.async,
        defer: script.defer,
        isThirdParty: !script.src.includes(window.location.origin),
      }));
    });
    
    const thirdPartyScripts = scripts.filter(script => script.isThirdParty);
    
    thirdPartyScripts.forEach(script => {
      // Third-party scripts should be async or defer
      expect(script.async || script.defer).toBeTruthy();
    });
    
    console.log('Third-party scripts:', thirdPartyScripts);
  });
});
