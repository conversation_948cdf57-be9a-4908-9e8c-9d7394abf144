// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://pbgxznogasjlkjmygyfp.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZ3h6bm9nYXNqbGtqbXlneWZwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4OTMwMzIsImV4cCI6MjA2NTQ2OTAzMn0.nEu0mbeDX-z9jW2xwkRNTvt3k280l7aSBDYu3G5dVWQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);