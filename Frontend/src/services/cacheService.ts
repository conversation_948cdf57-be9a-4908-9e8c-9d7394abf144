// =====================================================
// COMPREHENSIVE CACHING SERVICE
// Multi-level caching with Redis, browser, and API optimization
// =====================================================

import { supabase } from '@/lib/supabase';

// Types and Interfaces
export interface CacheConfig {
  ttl: number; // Time to live in seconds
  maxSize?: number; // Maximum cache size in bytes
  strategy: 'lru' | 'lfu' | 'fifo' | 'ttl';
  compression?: boolean;
  encryption?: boolean;
  namespace?: string;
}

export interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: number;
  ttl: number;
  size: number;
  accessCount: number;
  lastAccessed: number;
  compressed?: boolean;
  encrypted?: boolean;
}

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalSize: number;
  entryCount: number;
  averageResponseTime: number;
  evictions: number;
}

export interface CacheInvalidationRule {
  pattern: string;
  triggers: string[];
  dependencies: string[];
}

class CacheService {
  private memoryCache: Map<string, CacheEntry> = new Map();
  private cacheStats: CacheStats = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalSize: 0,
    entryCount: 0,
    averageResponseTime: 0,
    evictions: 0
  };

  private defaultConfig: CacheConfig = {
    ttl: 300, // 5 minutes
    maxSize: 50 * 1024 * 1024, // 50MB
    strategy: 'lru',
    compression: true,
    encryption: false,
    namespace: 'phcityrent'
  };

  private invalidationRules: CacheInvalidationRule[] = [];
  private redisClient: any = null; // Would be Redis client in production

  constructor() {
    this.initializeRedis();
    this.setupInvalidationRules();
    this.startCleanupInterval();
  }

  // Initialize Redis connection
  private async initializeRedis(): Promise<void> {
    try {
      // In production, this would initialize Redis client
      // For now, we'll simulate Redis with memory cache
      console.log('Redis cache initialized (simulated)');
    } catch (error) {
      console.warn('Redis initialization failed, using memory cache only:', error);
    }
  }

  // Setup cache invalidation rules
  private setupInvalidationRules(): void {
    this.invalidationRules = [
      {
        pattern: 'properties:*',
        triggers: ['property_created', 'property_updated', 'property_deleted'],
        dependencies: ['properties:list', 'properties:search', 'properties:stats']
      },
      {
        pattern: 'user:*:profile',
        triggers: ['profile_updated'],
        dependencies: ['user:*:dashboard', 'user:*:settings']
      },
      {
        pattern: 'api:properties:*',
        triggers: ['property_status_changed'],
        dependencies: ['api:properties:available', 'api:properties:featured']
      }
    ];
  }

  // Get from cache with multiple levels
  async get<T>(key: string, config: Partial<CacheConfig> = {}): Promise<T | null> {
    const startTime = Date.now();
    const cacheConfig = { ...this.defaultConfig, ...config };
    const namespacedKey = this.getNamespacedKey(key, cacheConfig.namespace);

    try {
      // Level 1: Memory cache
      const memoryResult = this.getFromMemory<T>(namespacedKey);
      if (memoryResult !== null) {
        this.recordCacheHit(Date.now() - startTime);
        return memoryResult;
      }

      // Level 2: Redis cache
      const redisResult = await this.getFromRedis<T>(namespacedKey);
      if (redisResult !== null) {
        // Store in memory cache for faster access
        await this.setInMemory(namespacedKey, redisResult, cacheConfig);
        this.recordCacheHit(Date.now() - startTime);
        return redisResult;
      }

      // Level 3: Browser cache (for client-side)
      if (typeof window !== 'undefined') {
        const browserResult = this.getFromBrowser<T>(namespacedKey);
        if (browserResult !== null) {
          // Store in higher levels
          await this.setInMemory(namespacedKey, browserResult, cacheConfig);
          await this.setInRedis(namespacedKey, browserResult, cacheConfig);
          this.recordCacheHit(Date.now() - startTime);
          return browserResult;
        }
      }

      this.recordCacheMiss(Date.now() - startTime);
      return null;
    } catch (error) {
      console.error('Cache get error:', error);
      this.recordCacheMiss(Date.now() - startTime);
      return null;
    }
  }

  // Set in cache with multiple levels
  async set<T>(key: string, value: T, config: Partial<CacheConfig> = {}): Promise<void> {
    const cacheConfig = { ...this.defaultConfig, ...config };
    const namespacedKey = this.getNamespacedKey(key, cacheConfig.namespace);

    try {
      // Set in all cache levels
      await Promise.all([
        this.setInMemory(namespacedKey, value, cacheConfig),
        this.setInRedis(namespacedKey, value, cacheConfig),
        typeof window !== 'undefined' ? this.setInBrowser(namespacedKey, value, cacheConfig) : Promise.resolve()
      ]);

      // Log cache operation
      await this.logCacheOperation('set', namespacedKey, Date.now());
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  // Delete from cache
  async delete(key: string, config: Partial<CacheConfig> = {}): Promise<void> {
    const cacheConfig = { ...this.defaultConfig, ...config };
    const namespacedKey = this.getNamespacedKey(key, cacheConfig.namespace);

    try {
      // Delete from all cache levels
      this.memoryCache.delete(namespacedKey);
      await this.deleteFromRedis(namespacedKey);
      
      if (typeof window !== 'undefined') {
        this.deleteFromBrowser(namespacedKey);
      }

      // Log cache operation
      await this.logCacheOperation('delete', namespacedKey, Date.now());
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  // Memory cache operations
  private getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key);
    
    if (!entry) return null;
    
    // Check TTL
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.memoryCache.delete(key);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    return entry.value as T;
  }

  private async setInMemory<T>(key: string, value: T, config: CacheConfig): Promise<void> {
    const size = this.calculateSize(value);
    
    // Check if we need to evict entries
    await this.evictIfNeeded(size, config);

    const entry: CacheEntry<T> = {
      key,
      value,
      timestamp: Date.now(),
      ttl: config.ttl,
      size,
      accessCount: 1,
      lastAccessed: Date.now(),
      compressed: config.compression,
      encrypted: config.encryption
    };

    this.memoryCache.set(key, entry);
    this.updateCacheStats();
  }

  // Redis cache operations (simulated)
  private async getFromRedis<T>(key: string): Promise<T | null> {
    try {
      // In production, this would use actual Redis client
      // For now, simulate Redis behavior
      return null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  private async setInRedis<T>(key: string, value: T, config: CacheConfig): Promise<void> {
    try {
      // In production, this would use actual Redis client
      // For now, simulate Redis behavior
      console.log(`Redis SET: ${key} (TTL: ${config.ttl}s)`);
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }

  private async deleteFromRedis(key: string): Promise<void> {
    try {
      // In production, this would use actual Redis client
      console.log(`Redis DELETE: ${key}`);
    } catch (error) {
      console.error('Redis delete error:', error);
    }
  }

  // Browser cache operations
  private getFromBrowser<T>(key: string): T | null {
    try {
      const stored = localStorage.getItem(key);
      if (!stored) return null;

      const entry = JSON.parse(stored);
      
      // Check TTL
      if (Date.now() - entry.timestamp > entry.ttl * 1000) {
        localStorage.removeItem(key);
        return null;
      }

      return entry.value as T;
    } catch (error) {
      console.error('Browser cache get error:', error);
      return null;
    }
  }

  private setInBrowser<T>(key: string, value: T, config: CacheConfig): void {
    try {
      const entry = {
        value,
        timestamp: Date.now(),
        ttl: config.ttl
      };

      localStorage.setItem(key, JSON.stringify(entry));
    } catch (error) {
      console.error('Browser cache set error:', error);
    }
  }

  private deleteFromBrowser(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Browser cache delete error:', error);
    }
  }

  // Cache with function memoization
  async memoize<T>(
    key: string,
    fn: () => Promise<T>,
    config: Partial<CacheConfig> = {}
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key, config);
    if (cached !== null) {
      return cached;
    }

    // Execute function and cache result
    const result = await fn();
    await this.set(key, result, config);
    
    return result;
  }

  // Batch operations
  async mget<T>(keys: string[], config: Partial<CacheConfig> = {}): Promise<(T | null)[]> {
    const promises = keys.map(key => this.get<T>(key, config));
    return Promise.all(promises);
  }

  async mset<T>(entries: Array<{ key: string; value: T }>, config: Partial<CacheConfig> = {}): Promise<void> {
    const promises = entries.map(entry => this.set(entry.key, entry.value, config));
    await Promise.all(promises);
  }

  // Cache invalidation
  async invalidate(pattern: string): Promise<void> {
    try {
      const regex = new RegExp(pattern.replace('*', '.*'));
      
      // Invalidate memory cache
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          this.memoryCache.delete(key);
        }
      }

      // Invalidate Redis cache
      await this.invalidateRedisPattern(pattern);

      // Invalidate browser cache
      if (typeof window !== 'undefined') {
        this.invalidateBrowserPattern(pattern);
      }

      // Log invalidation
      await this.logCacheOperation('invalidate', pattern, Date.now());
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }

  // Trigger-based invalidation
  async triggerInvalidation(trigger: string): Promise<void> {
    const rulesToApply = this.invalidationRules.filter(rule => 
      rule.triggers.includes(trigger)
    );

    for (const rule of rulesToApply) {
      await this.invalidate(rule.pattern);
      
      // Invalidate dependencies
      for (const dependency of rule.dependencies) {
        await this.invalidate(dependency);
      }
    }
  }

  // Cache warming
  async warmCache(entries: Array<{ key: string; fn: () => Promise<any> }>, config: Partial<CacheConfig> = {}): Promise<void> {
    const promises = entries.map(async entry => {
      try {
        const value = await entry.fn();
        await this.set(entry.key, value, config);
      } catch (error) {
        console.error(`Cache warming failed for key ${entry.key}:`, error);
      }
    });

    await Promise.all(promises);
  }

  // Cache statistics
  getStats(): CacheStats {
    this.updateCacheStats();
    return { ...this.cacheStats };
  }

  // Clear all caches
  async clear(): Promise<void> {
    try {
      // Clear memory cache
      this.memoryCache.clear();

      // Clear Redis cache
      await this.clearRedis();

      // Clear browser cache
      if (typeof window !== 'undefined') {
        this.clearBrowser();
      }

      // Reset stats
      this.cacheStats = {
        hits: 0,
        misses: 0,
        hitRate: 0,
        totalSize: 0,
        entryCount: 0,
        averageResponseTime: 0,
        evictions: 0
      };
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  // Utility methods
  private getNamespacedKey(key: string, namespace?: string): string {
    return namespace ? `${namespace}:${key}` : key;
  }

  private calculateSize(value: any): number {
    return JSON.stringify(value).length * 2; // Rough estimate
  }

  private async evictIfNeeded(newEntrySize: number, config: CacheConfig): Promise<void> {
    if (!config.maxSize) return;

    const currentSize = this.getCurrentCacheSize();
    
    if (currentSize + newEntrySize > config.maxSize) {
      await this.evictEntries(currentSize + newEntrySize - config.maxSize, config.strategy);
    }
  }

  private getCurrentCacheSize(): number {
    let totalSize = 0;
    for (const entry of this.memoryCache.values()) {
      totalSize += entry.size;
    }
    return totalSize;
  }

  private async evictEntries(bytesToEvict: number, strategy: CacheConfig['strategy']): Promise<void> {
    const entries = Array.from(this.memoryCache.entries());
    let evicted = 0;

    // Sort entries based on eviction strategy
    switch (strategy) {
      case 'lru':
        entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
        break;
      case 'lfu':
        entries.sort((a, b) => a[1].accessCount - b[1].accessCount);
        break;
      case 'fifo':
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        break;
      case 'ttl':
        entries.sort((a, b) => (a[1].timestamp + a[1].ttl * 1000) - (b[1].timestamp + b[1].ttl * 1000));
        break;
    }

    // Evict entries until we have enough space
    for (const [key, entry] of entries) {
      if (evicted >= bytesToEvict) break;
      
      this.memoryCache.delete(key);
      evicted += entry.size;
      this.cacheStats.evictions++;
    }
  }

  private recordCacheHit(responseTime: number): void {
    this.cacheStats.hits++;
    this.updateHitRate();
    this.updateAverageResponseTime(responseTime);
  }

  private recordCacheMiss(responseTime: number): void {
    this.cacheStats.misses++;
    this.updateHitRate();
    this.updateAverageResponseTime(responseTime);
  }

  private updateHitRate(): void {
    const total = this.cacheStats.hits + this.cacheStats.misses;
    this.cacheStats.hitRate = total > 0 ? (this.cacheStats.hits / total) * 100 : 0;
  }

  private updateAverageResponseTime(responseTime: number): void {
    const total = this.cacheStats.hits + this.cacheStats.misses;
    this.cacheStats.averageResponseTime = 
      ((this.cacheStats.averageResponseTime * (total - 1)) + responseTime) / total;
  }

  private updateCacheStats(): void {
    this.cacheStats.entryCount = this.memoryCache.size;
    this.cacheStats.totalSize = this.getCurrentCacheSize();
  }

  private async invalidateRedisPattern(pattern: string): Promise<void> {
    // In production, this would use Redis SCAN and DEL commands
    console.log(`Redis INVALIDATE: ${pattern}`);
  }

  private invalidateBrowserPattern(pattern: string): void {
    const regex = new RegExp(pattern.replace('*', '.*'));
    const keysToRemove: string[] = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && regex.test(key)) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
  }

  private async clearRedis(): Promise<void> {
    // In production, this would use Redis FLUSHDB command
    console.log('Redis FLUSHDB');
  }

  private clearBrowser(): void {
    localStorage.clear();
  }

  private async logCacheOperation(operation: string, key: string, timestamp: number): Promise<void> {
    try {
      await supabase
        .from('cache_metrics')
        .insert({
          cache_type: 'multi_level',
          cache_key: key,
          operation,
          response_time_ms: 0,
          timestamp: new Date(timestamp).toISOString()
        });
    } catch (error) {
      console.error('Failed to log cache operation:', error);
    }
  }

  private startCleanupInterval(): void {
    // Clean up expired entries every 5 minutes
    setInterval(() => {
      this.cleanupExpiredEntries();
    }, 5 * 60 * 1000);
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.memoryCache.entries()) {
      if (now - entry.timestamp > entry.ttl * 1000) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.memoryCache.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }
}

// Export singleton instance
export const cacheService = new CacheService();
export default cacheService;
