// =====================================================
// SECURITY MONITORING SERVICE
// Comprehensive security event monitoring and incident response
// =====================================================

import { supabase } from '@/lib/supabase';

// Types and Interfaces
export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  severity: SecuritySeverity;
  source: string;
  description: string;
  details: Record<string, any>;
  userId?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: number;
  resolved: boolean;
  resolvedAt?: number;
  resolvedBy?: string;
  tags: string[];
}

export type SecurityEventType = 
  | 'authentication_failure'
  | 'authorization_violation'
  | 'data_breach_attempt'
  | 'malicious_file_upload'
  | 'sql_injection_attempt'
  | 'xss_attempt'
  | 'csrf_attack'
  | 'rate_limit_violation'
  | 'suspicious_activity'
  | 'privilege_escalation'
  | 'data_exfiltration'
  | 'brute_force_attack'
  | 'account_takeover'
  | 'malware_detection'
  | 'vulnerability_exploit';

export type SecuritySeverity = 'low' | 'medium' | 'high' | 'critical';

export interface SecurityAlert {
  id: string;
  eventId: string;
  type: 'threshold_breach' | 'pattern_detection' | 'anomaly_detection' | 'manual_trigger';
  title: string;
  description: string;
  severity: SecuritySeverity;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  assignedTo?: string;
  createdAt: number;
  updatedAt: number;
  escalationLevel: number;
  automatedResponse?: string;
}

export interface SecurityRule {
  id: string;
  name: string;
  description: string;
  eventTypes: SecurityEventType[];
  conditions: SecurityCondition[];
  actions: SecurityAction[];
  isActive: boolean;
  priority: number;
  cooldownPeriod: number; // Minutes
  lastTriggered?: number;
}

export interface SecurityCondition {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'regex';
  value: any;
  timeWindow?: number; // Minutes
}

export interface SecurityAction {
  type: 'alert' | 'block_ip' | 'disable_account' | 'notify_admin' | 'log_event' | 'escalate';
  parameters: Record<string, any>;
  delay?: number; // Seconds
}

export interface ThreatIntelligence {
  ipAddress: string;
  threatType: 'malware' | 'botnet' | 'scanner' | 'spam' | 'phishing' | 'unknown';
  riskScore: number; // 0-100
  lastSeen: number;
  source: string;
  details: Record<string, any>;
}

export interface SecurityMetrics {
  totalEvents: number;
  eventsBySeverity: Record<SecuritySeverity, number>;
  eventsByType: Record<SecurityEventType, number>;
  topThreats: Array<{ ipAddress: string; count: number; riskScore: number }>;
  responseTime: {
    average: number;
    median: number;
    p95: number;
  };
  falsePositiveRate: number;
  blockedAttacks: number;
  activeAlerts: number;
}

class SecurityMonitoringService {
  private rules: Map<string, SecurityRule> = new Map();
  private threatIntelligence: Map<string, ThreatIntelligence> = new Map();
  private eventBuffer: SecurityEvent[] = [];
  private alertBuffer: SecurityAlert[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeDefaultRules();
    this.startMonitoring();
    this.loadThreatIntelligence();
  }

  // Initialize default security rules
  private initializeDefaultRules(): void {
    const defaultRules: SecurityRule[] = [
      {
        id: 'brute_force_detection',
        name: 'Brute Force Attack Detection',
        description: 'Detect multiple failed login attempts from same IP',
        eventTypes: ['authentication_failure'],
        conditions: [
          {
            field: 'ipAddress',
            operator: 'equals',
            value: '{{current_ip}}',
            timeWindow: 15
          },
          {
            field: 'count',
            operator: 'greater_than',
            value: 5
          }
        ],
        actions: [
          {
            type: 'alert',
            parameters: {
              severity: 'high',
              title: 'Brute Force Attack Detected',
              description: 'Multiple failed login attempts detected from IP: {{ipAddress}}'
            }
          },
          {
            type: 'block_ip',
            parameters: {
              duration: 3600, // 1 hour
              reason: 'Brute force attack'
            }
          }
        ],
        isActive: true,
        priority: 1,
        cooldownPeriod: 60
      },

      {
        id: 'sql_injection_detection',
        name: 'SQL Injection Detection',
        description: 'Detect SQL injection attempts',
        eventTypes: ['sql_injection_attempt'],
        conditions: [
          {
            field: 'severity',
            operator: 'in',
            value: ['high', 'critical']
          }
        ],
        actions: [
          {
            type: 'alert',
            parameters: {
              severity: 'critical',
              title: 'SQL Injection Attempt',
              description: 'SQL injection attempt detected from IP: {{ipAddress}}'
            }
          },
          {
            type: 'block_ip',
            parameters: {
              duration: 86400, // 24 hours
              reason: 'SQL injection attempt'
            }
          },
          {
            type: 'notify_admin',
            parameters: {
              urgency: 'immediate'
            }
          }
        ],
        isActive: true,
        priority: 1,
        cooldownPeriod: 30
      },

      {
        id: 'data_exfiltration_detection',
        name: 'Data Exfiltration Detection',
        description: 'Detect unusual data access patterns',
        eventTypes: ['data_exfiltration', 'suspicious_activity'],
        conditions: [
          {
            field: 'dataVolume',
            operator: 'greater_than',
            value: 1000000, // 1MB
            timeWindow: 10
          }
        ],
        actions: [
          {
            type: 'alert',
            parameters: {
              severity: 'critical',
              title: 'Potential Data Exfiltration',
              description: 'Unusual data access pattern detected'
            }
          },
          {
            type: 'escalate',
            parameters: {
              level: 2
            }
          }
        ],
        isActive: true,
        priority: 1,
        cooldownPeriod: 15
      },

      {
        id: 'malware_upload_detection',
        name: 'Malware Upload Detection',
        description: 'Detect malicious file uploads',
        eventTypes: ['malicious_file_upload', 'malware_detection'],
        conditions: [
          {
            field: 'severity',
            operator: 'equals',
            value: 'critical'
          }
        ],
        actions: [
          {
            type: 'alert',
            parameters: {
              severity: 'critical',
              title: 'Malware Upload Detected',
              description: 'Malicious file upload attempt blocked'
            }
          },
          {
            type: 'disable_account',
            parameters: {
              duration: 3600, // 1 hour
              reason: 'Malware upload attempt'
            }
          },
          {
            type: 'notify_admin',
            parameters: {
              urgency: 'immediate'
            }
          }
        ],
        isActive: true,
        priority: 1,
        cooldownPeriod: 5
      }
    ];

    defaultRules.forEach(rule => this.rules.set(rule.id, rule));
  }

  // Log security event
  async logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp' | 'resolved'>): Promise<string> {
    try {
      const securityEvent: SecurityEvent = {
        id: self.crypto.randomUUID(),
        timestamp: Date.now(),
        resolved: false,
        ...event
      };

      // Add to buffer for processing
      this.eventBuffer.push(securityEvent);

      // Store in database
      await supabase
        .from('security_events')
        .insert({
          event_id: securityEvent.id,
          event_type: securityEvent.type,
          severity: securityEvent.severity,
          source: securityEvent.source,
          description: securityEvent.description,
          details: securityEvent.details,
          user_id: securityEvent.userId,
          session_id: securityEvent.sessionId,
          ip_address: securityEvent.ipAddress,
          user_agent: securityEvent.userAgent,
          tags: securityEvent.tags,
          timestamp: new Date(securityEvent.timestamp).toISOString()
        });

      // Process event against rules
      await this.processEventAgainstRules(securityEvent);

      return securityEvent.id;
    } catch (error) {
      console.error('Failed to log security event:', error);
      throw error;
    }
  }

  // Process event against security rules
  private async processEventAgainstRules(event: SecurityEvent): Promise<void> {
    for (const rule of this.rules.values()) {
      if (!rule.isActive || !rule.eventTypes.includes(event.type)) {
        continue;
      }

      // Check cooldown period
      if (rule.lastTriggered && 
          Date.now() - rule.lastTriggered < rule.cooldownPeriod * 60 * 1000) {
        continue;
      }

      // Evaluate conditions
      const conditionsMet = await this.evaluateRuleConditions(rule, event);
      
      if (conditionsMet) {
        await this.executeRuleActions(rule, event);
        rule.lastTriggered = Date.now();
      }
    }
  }

  // Evaluate rule conditions
  private async evaluateRuleConditions(rule: SecurityRule, event: SecurityEvent): Promise<boolean> {
    for (const condition of rule.conditions) {
      const fieldValue = this.getFieldValue(event, condition.field);
      
      if (condition.timeWindow) {
        // Time-based condition - check events in time window
        const windowStart = Date.now() - (condition.timeWindow * 60 * 1000);
        const relatedEvents = await this.getEventsInTimeWindow(
          event.type,
          event.ipAddress,
          windowStart
        );

        if (condition.field === 'count') {
          if (!this.evaluateCondition(relatedEvents.length, condition.operator, condition.value)) {
            return false;
          }
        }
      } else {
        // Direct field condition
        if (!this.evaluateCondition(fieldValue, condition.operator, condition.value)) {
          return false;
        }
      }
    }

    return true;
  }

  // Get field value from event
  private getFieldValue(event: SecurityEvent, field: string): any {
    if (field.includes('.')) {
      const parts = field.split('.');
      let value = event as any;
      for (const part of parts) {
        value = value?.[part];
      }
      return value;
    }
    return (event as any)[field];
  }

  // Evaluate single condition
  private evaluateCondition(value: any, operator: string, expected: any): boolean {
    switch (operator) {
      case 'equals':
        return value === expected;
      case 'contains':
        return typeof value === 'string' && value.includes(expected);
      case 'greater_than':
        return typeof value === 'number' && value > expected;
      case 'less_than':
        return typeof value === 'number' && value < expected;
      case 'in':
        return Array.isArray(expected) && expected.includes(value);
      case 'regex':
        return typeof value === 'string' && new RegExp(expected).test(value);
      default:
        return false;
    }
  }

  // Get events in time window
  private async getEventsInTimeWindow(
    eventType: SecurityEventType,
    ipAddress: string,
    windowStart: number
  ): Promise<SecurityEvent[]> {
    try {
      const { data, error } = await supabase
        .from('security_events')
        .select('*')
        .eq('event_type', eventType)
        .eq('ip_address', ipAddress)
        .gte('timestamp', new Date(windowStart).toISOString());

      if (error) throw error;

      return data?.map(record => ({
        id: record.event_id,
        type: record.event_type,
        severity: record.severity,
        source: record.source,
        description: record.description,
        details: record.details,
        userId: record.user_id,
        sessionId: record.session_id,
        ipAddress: record.ip_address,
        userAgent: record.user_agent,
        timestamp: new Date(record.timestamp).getTime(),
        resolved: record.resolved,
        resolvedAt: record.resolved_at ? new Date(record.resolved_at).getTime() : undefined,
        resolvedBy: record.resolved_by,
        tags: record.tags || []
      })) || [];
    } catch (error) {
      console.error('Failed to get events in time window:', error);
      return [];
    }
  }

  // Execute rule actions
  private async executeRuleActions(rule: SecurityRule, event: SecurityEvent): Promise<void> {
    for (const action of rule.actions) {
      try {
        if (action.delay) {
          setTimeout(() => this.executeAction(action, event), action.delay * 1000);
        } else {
          await this.executeAction(action, event);
        }
      } catch (error) {
        console.error(`Failed to execute action ${action.type}:`, error);
      }
    }
  }

  // Execute individual action
  private async executeAction(action: SecurityAction, event: SecurityEvent): Promise<void> {
    switch (action.type) {
      case 'alert':
        await this.createAlert(action.parameters, event);
        break;
      case 'block_ip':
        await this.blockIP(event.ipAddress, action.parameters);
        break;
      case 'disable_account':
        if (event.userId) {
          await this.disableAccount(event.userId, action.parameters);
        }
        break;
      case 'notify_admin':
        await this.notifyAdmin(event, action.parameters);
        break;
      case 'log_event':
        console.log(`Security action triggered: ${JSON.stringify(action.parameters)}`);
        break;
      case 'escalate':
        await this.escalateAlert(event, action.parameters);
        break;
    }
  }

  // Create security alert
  private async createAlert(parameters: any, event: SecurityEvent): Promise<void> {
    const alert: SecurityAlert = {
      id: self.crypto.randomUUID(),
      eventId: event.id,
      type: 'threshold_breach',
      title: this.interpolateString(parameters.title, event),
      description: this.interpolateString(parameters.description, event),
      severity: parameters.severity || event.severity,
      status: 'open',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      escalationLevel: 1
    };

    this.alertBuffer.push(alert);

    await supabase
      .from('security_alerts')
      .insert({
        alert_id: alert.id,
        event_id: alert.eventId,
        alert_type: alert.type,
        title: alert.title,
        description: alert.description,
        severity: alert.severity,
        status: alert.status,
        escalation_level: alert.escalationLevel,
        created_at: new Date(alert.createdAt).toISOString(),
        updated_at: new Date(alert.updatedAt).toISOString()
      });
  }

  // Block IP address
  private async blockIP(ipAddress: string, parameters: any): Promise<void> {
    const expiresAt = Date.now() + (parameters.duration * 1000);
    
    await supabase
      .from('blocked_ips')
      .insert({
        ip_address: ipAddress,
        reason: parameters.reason,
        blocked_at: new Date().toISOString(),
        expires_at: new Date(expiresAt).toISOString(),
        is_active: true
      });
  }

  // Disable user account
  private async disableAccount(userId: string, parameters: any): Promise<void> {
    const expiresAt = Date.now() + (parameters.duration * 1000);
    
    await supabase
      .from('account_suspensions')
      .insert({
        user_id: userId,
        reason: parameters.reason,
        suspended_at: new Date().toISOString(),
        expires_at: new Date(expiresAt).toISOString(),
        is_active: true
      });
  }

  // Notify administrators
  private async notifyAdmin(event: SecurityEvent, parameters: any): Promise<void> {
    // In production, this would send notifications via email, SMS, Slack, etc.
    console.log(`ADMIN NOTIFICATION: ${event.type} - ${event.description}`);
    
    await supabase
      .from('admin_notifications')
      .insert({
        event_id: event.id,
        urgency: parameters.urgency,
        message: `Security Event: ${event.type} - ${event.description}`,
        sent_at: new Date().toISOString()
      });
  }

  // Escalate alert
  private async escalateAlert(event: SecurityEvent, parameters: any): Promise<void> {
    const existingAlert = this.alertBuffer.find(alert => alert.eventId === event.id);
    
    if (existingAlert) {
      existingAlert.escalationLevel = parameters.level;
      existingAlert.updatedAt = Date.now();
      
      await supabase
        .from('security_alerts')
        .update({
          escalation_level: existingAlert.escalationLevel,
          updated_at: new Date(existingAlert.updatedAt).toISOString()
        })
        .eq('alert_id', existingAlert.id);
    }
  }

  // Interpolate string with event data
  private interpolateString(template: string, event: SecurityEvent): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return (event as any)[key] || match;
    });
  }

  // Load threat intelligence data
  private async loadThreatIntelligence(): Promise<void> {
    try {
      // In production, this would load from threat intelligence feeds
      const { data, error } = await supabase
        .from('threat_intelligence')
        .select('*')
        .eq('is_active', true);

      if (error) throw error;

      data?.forEach(threat => {
        this.threatIntelligence.set(threat.ip_address, {
          ipAddress: threat.ip_address,
          threatType: threat.threat_type,
          riskScore: threat.risk_score,
          lastSeen: new Date(threat.last_seen).getTime(),
          source: threat.source,
          details: threat.details || {}
        });
      });
    } catch (error) {
      console.error('Failed to load threat intelligence:', error);
    }
  }

  // Check IP against threat intelligence
  checkThreatIntelligence(ipAddress: string): ThreatIntelligence | null {
    return this.threatIntelligence.get(ipAddress) || null;
  }

  // Start monitoring
  private startMonitoring(): void {
    this.monitoringInterval = setInterval(() => {
      this.processEventBuffer();
      this.updateThreatIntelligence();
    }, 30000); // Process every 30 seconds
  }

  // Process event buffer
  private processEventBuffer(): void {
    // Process buffered events for pattern detection
    if (this.eventBuffer.length > 100) {
      this.eventBuffer = this.eventBuffer.slice(-100); // Keep last 100 events
    }
  }

  // Update threat intelligence
  private async updateThreatIntelligence(): Promise<void> {
    // In production, this would fetch updates from threat intelligence feeds
    // For now, we'll just clean up old entries
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    
    for (const [ip, threat] of this.threatIntelligence.entries()) {
      if (threat.lastSeen < oneWeekAgo) {
        this.threatIntelligence.delete(ip);
      }
    }
  }

  // Get security metrics
  async getSecurityMetrics(timeRange: 'hour' | 'day' | 'week' = 'day'): Promise<SecurityMetrics> {
    try {
      const hours = timeRange === 'hour' ? 1 : timeRange === 'day' ? 24 : 168;
      const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

      const { data: events, error } = await supabase
        .from('security_events')
        .select('*')
        .gte('timestamp', startTime.toISOString());

      if (error) throw error;

      const metrics: SecurityMetrics = {
        totalEvents: events?.length || 0,
        eventsBySeverity: { low: 0, medium: 0, high: 0, critical: 0 },
        eventsByType: {} as any,
        topThreats: [],
        responseTime: { average: 0, median: 0, p95: 0 },
        falsePositiveRate: 0,
        blockedAttacks: 0,
        activeAlerts: this.alertBuffer.filter(a => a.status === 'open').length
      };

      // Calculate metrics
      const ipCounts: Record<string, number> = {};
      const responseTimes: number[] = [];

      events?.forEach(event => {
        metrics.eventsBySeverity[event.severity as SecuritySeverity]++;
        metrics.eventsByType[event.event_type as SecurityEventType] = 
          (metrics.eventsByType[event.event_type as SecurityEventType] || 0) + 1;

        ipCounts[event.ip_address] = (ipCounts[event.ip_address] || 0) + 1;

        if (event.resolved && event.resolved_at) {
          const responseTime = new Date(event.resolved_at).getTime() - new Date(event.timestamp).getTime();
          responseTimes.push(responseTime);
        }
      });

      // Top threats
      metrics.topThreats = Object.entries(ipCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([ip, count]) => ({
          ipAddress: ip,
          count,
          riskScore: this.threatIntelligence.get(ip)?.riskScore || 0
        }));

      // Response time metrics
      if (responseTimes.length > 0) {
        responseTimes.sort((a, b) => a - b);
        metrics.responseTime.average = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
        metrics.responseTime.median = responseTimes[Math.floor(responseTimes.length / 2)];
        metrics.responseTime.p95 = responseTimes[Math.floor(responseTimes.length * 0.95)];
      }

      return metrics;
    } catch (error) {
      console.error('Failed to get security metrics:', error);
      throw error;
    }
  }

  // Add security rule
  addRule(rule: SecurityRule): void {
    this.rules.set(rule.id, rule);
  }

  // Update security rule
  updateRule(ruleId: string, updates: Partial<SecurityRule>): boolean {
    const rule = this.rules.get(ruleId);
    if (!rule) return false;

    this.rules.set(ruleId, { ...rule, ...updates });
    return true;
  }

  // Remove security rule
  removeRule(ruleId: string): boolean {
    return this.rules.delete(ruleId);
  }

  // Get all rules
  getRules(): SecurityRule[] {
    return Array.from(this.rules.values());
  }

  // Resolve security event
  async resolveEvent(eventId: string, resolvedBy: string): Promise<void> {
    try {
      await supabase
        .from('security_events')
        .update({
          resolved: true,
          resolved_at: new Date().toISOString(),
          resolved_by: resolvedBy
        })
        .eq('event_id', eventId);
    } catch (error) {
      console.error('Failed to resolve security event:', error);
      throw error;
    }
  }

  // Update alert status
  async updateAlertStatus(alertId: string, status: SecurityAlert['status'], assignedTo?: string): Promise<void> {
    try {
      const alert = this.alertBuffer.find(a => a.id === alertId);
      if (alert) {
        alert.status = status;
        alert.updatedAt = Date.now();
        if (assignedTo) alert.assignedTo = assignedTo;
      }

      await supabase
        .from('security_alerts')
        .update({
          status,
          assigned_to: assignedTo,
          updated_at: new Date().toISOString()
        })
        .eq('alert_id', alertId);
    } catch (error) {
      console.error('Failed to update alert status:', error);
      throw error;
    }
  }

  // Cleanup on shutdown
  destroy(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }
}

// Export singleton instance
export const securityMonitoringService = new SecurityMonitoringService();
export default securityMonitoringService;
