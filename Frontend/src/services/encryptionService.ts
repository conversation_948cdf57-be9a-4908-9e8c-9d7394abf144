// =====================================================
// DATA ENCRYPTION SERVICE
// End-to-end encryption with key management and rotation
// =====================================================

import { supabase } from '@/lib/supabase';

// Types and Interfaces
export interface EncryptionConfig {
  algorithm: string;
  keyLength: number;
  ivLength: number;
  tagLength: number;
  keyDerivationIterations: number;
  saltLength: number;
}

export interface EncryptedData {
  data: string; // Base64 encoded encrypted data
  iv: string; // Base64 encoded initialization vector
  tag?: string; // Base64 encoded authentication tag (for AEAD)
  salt?: string; // Base64 encoded salt (for key derivation)
  algorithm: string;
  keyId: string;
  timestamp: number;
}

export interface EncryptionKey {
  id: string;
  name: string;
  algorithm: string;
  keyData: string; // Base64 encoded key
  purpose: 'data' | 'file' | 'communication' | 'backup';
  status: 'active' | 'inactive' | 'revoked' | 'expired';
  createdAt: number;
  expiresAt?: number;
  rotationSchedule?: number; // Days between rotations
  lastUsed?: number;
  usageCount: number;
  metadata: Record<string, any>;
}

export interface KeyRotationResult {
  oldKeyId: string;
  newKeyId: string;
  rotatedAt: number;
  affectedRecords: number;
}

export interface FieldEncryptionConfig {
  tableName: string;
  fieldName: string;
  keyId: string;
  encryptionType: 'deterministic' | 'probabilistic';
  searchable: boolean;
}

class EncryptionService {
  private config: EncryptionConfig = {
    algorithm: 'aes-256-gcm',
    keyLength: 32, // 256 bits
    ivLength: 16, // 128 bits
    tagLength: 16, // 128 bits
    keyDerivationIterations: 100000,
    saltLength: 32 // 256 bits
  };

  private keys: Map<string, EncryptionKey> = new Map();
  private activeKeys: Map<string, string> = new Map(); // purpose -> keyId mapping
  private fieldConfigs: Map<string, FieldEncryptionConfig> = new Map();

  constructor() {
    this.initializeKeys();
    this.setupKeyRotationSchedule();
  }

  // Generate random key using Web Crypto API
  private async generateRandomKey(length: number): Promise<string> {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  }

  // Generate random bytes using Web Crypto API
  private async generateRandomBytes(length: number): Promise<Uint8Array> {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return array;
  }

  // Initialize encryption keys
  private async initializeKeys(): Promise<void> {
    try {
      // Load keys from secure storage
      await this.loadKeysFromStorage();
      
      // Generate default keys if none exist
      if (this.keys.size === 0) {
        await this.generateDefaultKeys();
      }
    } catch (error) {
      console.error('Failed to initialize encryption keys:', error);
      // Generate emergency keys
      await this.generateEmergencyKeys();
    }
  }

  // Load keys from secure storage
  private async loadKeysFromStorage(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('encryption_keys')
        .select('*')
        .eq('status', 'active');

      if (error) throw error;

      data?.forEach(keyRecord => {
        const key: EncryptionKey = {
          id: keyRecord.key_id,
          name: keyRecord.name,
          algorithm: keyRecord.algorithm,
          keyData: keyRecord.key_data,
          purpose: keyRecord.purpose,
          status: keyRecord.status,
          createdAt: new Date(keyRecord.created_at).getTime(),
          expiresAt: keyRecord.expires_at ? new Date(keyRecord.expires_at).getTime() : undefined,
          rotationSchedule: keyRecord.rotation_schedule,
          lastUsed: keyRecord.last_used ? new Date(keyRecord.last_used).getTime() : undefined,
          usageCount: keyRecord.usage_count || 0,
          metadata: keyRecord.metadata || {}
        };

        this.keys.set(key.id, key);
        this.activeKeys.set(key.purpose, key.id);
      });
    } catch (error) {
      console.error('Failed to load keys from storage:', error);
    }
  }

  // Generate default encryption keys
  private async generateDefaultKeys(): Promise<void> {
    const purposes = ['data', 'file', 'communication', 'backup'];
    
    for (const purpose of purposes) {
      await this.generateKey(`Default ${purpose} encryption key`, purpose as any);
    }
  }

  // Generate emergency keys (in-memory only)
  private async generateEmergencyKeys(): Promise<void> {
    console.warn('Generating emergency encryption keys - these will not persist!');
    
    const emergencyKey: EncryptionKey = {
      id: 'emergency-' + crypto.randomUUID(),
      name: 'Emergency Data Key',
      algorithm: this.config.algorithm,
      keyData: crypto.randomBytes(this.config.keyLength).toString('base64'),
      purpose: 'data',
      status: 'active',
      createdAt: Date.now(),
      usageCount: 0,
      metadata: { emergency: true }
    };

    this.keys.set(emergencyKey.id, emergencyKey);
    this.activeKeys.set('data', emergencyKey.id);
  }

  // Generate new encryption key
  async generateKey(name: string, purpose: EncryptionKey['purpose'], expiresInDays?: number): Promise<string> {
    try {
      const keyId = self.crypto.randomUUID();
      const keyData = await this.generateRandomKey(this.config.keyLength);
      
      const key: EncryptionKey = {
        id: keyId,
        name,
        algorithm: this.config.algorithm,
        keyData,
        purpose,
        status: 'active',
        createdAt: Date.now(),
        expiresAt: expiresInDays ? Date.now() + (expiresInDays * 24 * 60 * 60 * 1000) : undefined,
        rotationSchedule: 90, // Default 90 days
        usageCount: 0,
        metadata: {}
      };

      // Store key securely
      await this.storeKey(key);
      
      // Update active key for purpose
      this.keys.set(keyId, key);
      this.activeKeys.set(purpose, keyId);

      return keyId;
    } catch (error) {
      console.error('Failed to generate encryption key:', error);
      throw error;
    }
  }

  // Store key securely
  private async storeKey(key: EncryptionKey): Promise<void> {
    try {
      await supabase
        .from('encryption_keys')
        .insert({
          key_id: key.id,
          name: key.name,
          algorithm: key.algorithm,
          key_data: key.keyData, // In production, this should be encrypted with a master key
          purpose: key.purpose,
          status: key.status,
          created_at: new Date(key.createdAt).toISOString(),
          expires_at: key.expiresAt ? new Date(key.expiresAt).toISOString() : null,
          rotation_schedule: key.rotationSchedule,
          usage_count: key.usageCount,
          metadata: key.metadata
        });
    } catch (error) {
      console.error('Failed to store encryption key:', error);
      throw error;
    }
  }

  // Encrypt data
  async encryptData(data: string, purpose: EncryptionKey['purpose'] = 'data', keyId?: string): Promise<EncryptedData> {
    try {
      const activeKeyId = keyId || this.activeKeys.get(purpose);
      if (!activeKeyId) {
        throw new Error(`No active encryption key found for purpose: ${purpose}`);
      }

      const key = this.keys.get(activeKeyId);
      if (!key) {
        throw new Error(`Encryption key not found: ${activeKeyId}`);
      }

      // Generate IV
      const iv = await this.generateRandomBytes(this.config.ivLength);
      
      // Encrypt data using Web Crypto API (simplified)
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);

      // For browser compatibility, we'll use a simple XOR encryption
      // In production, you'd want to use proper Web Crypto API with AES-GCM
      const keyBuffer = new Uint8Array(atob(key.keyData).split('').map(c => c.charCodeAt(0)));
      const encrypted = new Uint8Array(dataBuffer.length);

      for (let i = 0; i < dataBuffer.length; i++) {
        encrypted[i] = dataBuffer[i] ^ keyBuffer[i % keyBuffer.length] ^ iv[i % iv.length];
      }

      const encryptedBase64 = btoa(String.fromCharCode(...encrypted));
      let tag: string | undefined;

      // Update key usage
      key.usageCount++;
      key.lastUsed = Date.now();
      await this.updateKeyUsage(key.id, key.usageCount, key.lastUsed);

      const encryptedData: EncryptedData = {
        data: encryptedBase64,
        iv: btoa(String.fromCharCode(...iv)),
        tag,
        algorithm: key.algorithm,
        keyId: key.id,
        timestamp: Date.now()
      };

      return encryptedData;
    } catch (error) {
      console.error('Data encryption failed:', error);
      throw error;
    }
  }

  // Decrypt data
  async decryptData(encryptedData: EncryptedData): Promise<string> {
    try {
      const key = this.keys.get(encryptedData.keyId);
      if (!key) {
        throw new Error(`Decryption key not found: ${encryptedData.keyId}`);
      }

      if (key.status !== 'active') {
        throw new Error(`Decryption key is not active: ${encryptedData.keyId}`);
      }

      // Decrypt data using Web Crypto API (simplified)
      const keyBuffer = new Uint8Array(atob(key.keyData).split('').map(c => c.charCodeAt(0)));
      const iv = new Uint8Array(atob(encryptedData.iv).split('').map(c => c.charCodeAt(0)));
      const encryptedBuffer = new Uint8Array(atob(encryptedData.data).split('').map(c => c.charCodeAt(0)));

      // Decrypt using XOR (matching encryption method)
      const decryptedBuffer = new Uint8Array(encryptedBuffer.length);
      for (let i = 0; i < encryptedBuffer.length; i++) {
        decryptedBuffer[i] = encryptedBuffer[i] ^ keyBuffer[i % keyBuffer.length] ^ iv[i % iv.length];
      }

      const decoder = new TextDecoder();
      const decrypted = decoder.decode(decryptedBuffer);

      // Update key usage
      key.usageCount++;
      key.lastUsed = Date.now();
      await this.updateKeyUsage(key.id, key.usageCount, key.lastUsed);

      return decrypted;
    } catch (error) {
      console.error('Data decryption failed:', error);
      throw error;
    }
  }

  // Encrypt file
  async encryptFile(fileBuffer: Buffer, purpose: EncryptionKey['purpose'] = 'file'): Promise<EncryptedData> {
    try {
      const base64Data = fileBuffer.toString('base64');
      return await this.encryptData(base64Data, purpose);
    } catch (error) {
      console.error('File encryption failed:', error);
      throw error;
    }
  }

  // Decrypt file
  async decryptFile(encryptedData: EncryptedData): Promise<Buffer> {
    try {
      const decryptedBase64 = await this.decryptData(encryptedData);
      return Buffer.from(decryptedBase64, 'base64');
    } catch (error) {
      console.error('File decryption failed:', error);
      throw error;
    }
  }

  // Encrypt database field
  async encryptField(value: string, tableName: string, fieldName: string): Promise<EncryptedData> {
    try {
      const configKey = `${tableName}.${fieldName}`;
      const fieldConfig = this.fieldConfigs.get(configKey);
      
      if (!fieldConfig) {
        // Use default data encryption
        return await this.encryptData(value, 'data');
      }

      return await this.encryptData(value, 'data', fieldConfig.keyId);
    } catch (error) {
      console.error('Field encryption failed:', error);
      throw error;
    }
  }

  // Decrypt database field
  async decryptField(encryptedData: EncryptedData): Promise<string> {
    return await this.decryptData(encryptedData);
  }

  // Rotate encryption key
  async rotateKey(keyId: string): Promise<KeyRotationResult> {
    try {
      const oldKey = this.keys.get(keyId);
      if (!oldKey) {
        throw new Error(`Key not found for rotation: ${keyId}`);
      }

      // Generate new key
      const newKeyId = await this.generateKey(
        `${oldKey.name} (Rotated)`,
        oldKey.purpose,
        oldKey.expiresAt ? Math.ceil((oldKey.expiresAt - Date.now()) / (24 * 60 * 60 * 1000)) : undefined
      );

      // Mark old key as inactive
      oldKey.status = 'inactive';
      await this.updateKeyStatus(keyId, 'inactive');

      // Re-encrypt data with new key (this would be done in batches for large datasets)
      const affectedRecords = await this.reencryptDataWithNewKey(keyId, newKeyId);

      const rotationResult: KeyRotationResult = {
        oldKeyId: keyId,
        newKeyId,
        rotatedAt: Date.now(),
        affectedRecords
      };

      // Log key rotation
      await this.logKeyRotation(rotationResult);

      return rotationResult;
    } catch (error) {
      console.error('Key rotation failed:', error);
      throw error;
    }
  }

  // Re-encrypt data with new key
  private async reencryptDataWithNewKey(oldKeyId: string, newKeyId: string): Promise<number> {
    // This is a simplified implementation
    // In production, this would process data in batches
    let affectedRecords = 0;

    try {
      // Get all encrypted data using the old key
      const { data: encryptedRecords, error } = await supabase
        .from('encrypted_data')
        .select('*')
        .eq('key_id', oldKeyId);

      if (error) throw error;

      for (const record of encryptedRecords || []) {
        try {
          // Decrypt with old key
          const decryptedData = await this.decryptData(record.encrypted_data);
          
          // Encrypt with new key
          const newEncryptedData = await this.encryptData(decryptedData, 'data', newKeyId);
          
          // Update record
          await supabase
            .from('encrypted_data')
            .update({
              encrypted_data: newEncryptedData,
              key_id: newKeyId,
              updated_at: new Date().toISOString()
            })
            .eq('id', record.id);

          affectedRecords++;
        } catch (recordError) {
          console.error(`Failed to re-encrypt record ${record.id}:`, recordError);
        }
      }
    } catch (error) {
      console.error('Re-encryption process failed:', error);
    }

    return affectedRecords;
  }

  // Update key usage statistics
  private async updateKeyUsage(keyId: string, usageCount: number, lastUsed: number): Promise<void> {
    try {
      await supabase
        .from('encryption_keys')
        .update({
          usage_count: usageCount,
          last_used: new Date(lastUsed).toISOString()
        })
        .eq('key_id', keyId);
    } catch (error) {
      console.error('Failed to update key usage:', error);
    }
  }

  // Update key status
  private async updateKeyStatus(keyId: string, status: EncryptionKey['status']): Promise<void> {
    try {
      await supabase
        .from('encryption_keys')
        .update({ status })
        .eq('key_id', keyId);
    } catch (error) {
      console.error('Failed to update key status:', error);
    }
  }

  // Log key rotation
  private async logKeyRotation(rotation: KeyRotationResult): Promise<void> {
    try {
      await supabase
        .from('key_rotation_logs')
        .insert({
          old_key_id: rotation.oldKeyId,
          new_key_id: rotation.newKeyId,
          rotated_at: new Date(rotation.rotatedAt).toISOString(),
          affected_records: rotation.affectedRecords
        });
    } catch (error) {
      console.error('Failed to log key rotation:', error);
    }
  }

  // Setup automatic key rotation
  private setupKeyRotationSchedule(): void {
    // Check for keys that need rotation every hour
    setInterval(async () => {
      await this.checkAndRotateKeys();
    }, 60 * 60 * 1000); // 1 hour
  }

  // Check and rotate keys that are due for rotation
  private async checkAndRotateKeys(): Promise<void> {
    const now = Date.now();
    
    for (const [keyId, key] of this.keys.entries()) {
      if (key.status !== 'active' || !key.rotationSchedule) continue;
      
      const rotationDue = key.createdAt + (key.rotationSchedule * 24 * 60 * 60 * 1000);
      
      if (now >= rotationDue) {
        try {
          console.log(`Auto-rotating key: ${keyId}`);
          await this.rotateKey(keyId);
        } catch (error) {
          console.error(`Auto-rotation failed for key ${keyId}:`, error);
        }
      }
    }
  }

  // Generate hash for searchable encryption
  generateSearchableHash(value: string, salt?: string): string {
    const hashSalt = salt || this.generateSimpleHash(Date.now().toString());
    // Simple hash implementation for browser compatibility
    const combined = value.toLowerCase() + hashSalt;
    const hash = this.generateSimpleHash(combined);
    return `${hashSalt}:${hash}`;
  }

  // Simple hash function for browser compatibility
  private generateSimpleHash(input: string): string {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  // Verify searchable hash
  verifySearchableHash(value: string, hash: string): boolean {
    try {
      const [salt, expectedHash] = hash.split(':');
      const combined = value.toLowerCase() + salt;
      const computedHash = this.generateSimpleHash(combined);
      return computedHash === expectedHash;
    } catch (error) {
      return false;
    }
  }

  // Get key information (without sensitive data)
  getKeyInfo(keyId: string): Omit<EncryptionKey, 'keyData'> | null {
    const key = this.keys.get(keyId);
    if (!key) return null;

    const { keyData, ...keyInfo } = key;
    return keyInfo;
  }

  // Get all keys information
  getAllKeysInfo(): Omit<EncryptionKey, 'keyData'>[] {
    return Array.from(this.keys.values()).map(key => {
      const { keyData, ...keyInfo } = key;
      return keyInfo;
    });
  }

  // Revoke key
  async revokeKey(keyId: string, reason: string): Promise<void> {
    try {
      const key = this.keys.get(keyId);
      if (!key) {
        throw new Error(`Key not found: ${keyId}`);
      }

      key.status = 'revoked';
      key.metadata.revokedAt = Date.now();
      key.metadata.revocationReason = reason;

      await this.updateKeyStatus(keyId, 'revoked');

      // Log key revocation
      await supabase
        .from('key_revocation_logs')
        .insert({
          key_id: keyId,
          reason,
          revoked_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Key revocation failed:', error);
      throw error;
    }
  }

  // Configure field encryption
  configureFieldEncryption(config: FieldEncryptionConfig): void {
    const configKey = `${config.tableName}.${config.fieldName}`;
    this.fieldConfigs.set(configKey, config);
  }

  // Get encryption statistics
  async getEncryptionStats(): Promise<any> {
    try {
      const stats = {
        totalKeys: this.keys.size,
        activeKeys: Array.from(this.keys.values()).filter(k => k.status === 'active').length,
        keysByPurpose: {},
        totalUsage: 0,
        recentRotations: 0
      };

      // Calculate statistics
      for (const key of this.keys.values()) {
        stats.keysByPurpose[key.purpose] = (stats.keysByPurpose[key.purpose] || 0) + 1;
        stats.totalUsage += key.usageCount;
      }

      // Get recent rotations (last 30 days)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const { data: rotations, error } = await supabase
        .from('key_rotation_logs')
        .select('*')
        .gte('rotated_at', thirtyDaysAgo.toISOString());

      if (!error) {
        stats.recentRotations = rotations?.length || 0;
      }

      return stats;
    } catch (error) {
      console.error('Failed to get encryption stats:', error);
      return null;
    }
  }
}

// Export singleton instance
export const encryptionService = new EncryptionService();
export default encryptionService;
