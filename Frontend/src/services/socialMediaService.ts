// =====================================================
// SOCIAL MEDIA INTEGRATION SERVICE
// Comprehensive social media features for PHCityRent
// =====================================================

import { supabase } from '@/lib/supabase';

// Types and Interfaces
export interface SocialPlatform {
  id: string;
  name: string;
  icon: string;
  color: string;
  shareUrl: string;
  apiEndpoint?: string;
}

export interface ShareContent {
  title: string;
  description: string;
  url: string;
  imageUrl?: string;
  hashtags?: string[];
  price?: number;
  location?: string;
  propertyType?: string;
}

export interface SocialLoginProvider {
  provider: 'google' | 'facebook' | 'twitter' | 'linkedin';
  clientId: string;
  redirectUri: string;
  scope: string[];
}

export interface UserGeneratedContent {
  id: string;
  userId: string;
  propertyId: string;
  platform: string;
  contentType: 'review' | 'photo' | 'video' | 'story';
  content: string;
  mediaUrls: string[];
  rating?: number;
  likes: number;
  shares: number;
  comments: number;
  isVerified: boolean;
  createdAt: string;
}

export interface SocialMetrics {
  platform: string;
  shares: number;
  likes: number;
  comments: number;
  reach: number;
  engagement: number;
  clicks: number;
}

export interface SocialCampaign {
  id: string;
  name: string;
  description: string;
  platforms: string[];
  content: ShareContent;
  schedule: {
    startDate: string;
    endDate: string;
    postTimes: string[];
  };
  targeting: {
    demographics: string[];
    interests: string[];
    locations: string[];
  };
  budget?: number;
  status: 'draft' | 'scheduled' | 'active' | 'completed' | 'paused';
  metrics: SocialMetrics[];
}

class SocialMediaService {
  private platforms: SocialPlatform[] = [
    {
      id: 'facebook',
      name: 'Facebook',
      icon: '📘',
      color: '#1877F2',
      shareUrl: 'https://www.facebook.com/sharer/sharer.php',
      apiEndpoint: 'https://graph.facebook.com/v18.0'
    },
    {
      id: 'twitter',
      name: 'Twitter',
      icon: '🐦',
      color: '#1DA1F2',
      shareUrl: 'https://twitter.com/intent/tweet',
      apiEndpoint: 'https://api.twitter.com/2'
    },
    {
      id: 'instagram',
      name: 'Instagram',
      icon: '📷',
      color: '#E4405F',
      shareUrl: 'https://www.instagram.com',
      apiEndpoint: 'https://graph.instagram.com'
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      icon: '💼',
      color: '#0A66C2',
      shareUrl: 'https://www.linkedin.com/sharing/share-offsite',
      apiEndpoint: 'https://api.linkedin.com/v2'
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: '💬',
      color: '#25D366',
      shareUrl: 'https://wa.me',
      apiEndpoint: 'https://graph.facebook.com/v18.0'
    },
    {
      id: 'telegram',
      name: 'Telegram',
      icon: '✈️',
      color: '#0088CC',
      shareUrl: 'https://t.me/share/url'
    }
  ];

  private apiKeys: Record<string, string> = {
    facebook: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID || '',
    twitter: process.env.NEXT_PUBLIC_TWITTER_API_KEY || '',
    instagram: process.env.NEXT_PUBLIC_INSTAGRAM_APP_ID || '',
    linkedin: process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID || '',
    google: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || ''
  };

  // Share property on social media platforms
  async shareProperty(content: ShareContent, platforms: string[]): Promise<{ success: boolean; results: Record<string, any> }> {
    try {
      const results: Record<string, any> = {};

      for (const platformId of platforms) {
        const platform = this.platforms.find(p => p.id === platformId);
        if (!platform) continue;

        try {
          const shareResult = await this.shareOnPlatform(platform, content);
          results[platformId] = { success: true, data: shareResult };
        } catch (error) {
          results[platformId] = { success: false, error: error.message };
        }
      }

      // Log sharing activity
      await this.logSharingActivity(content, platforms, results);

      return { success: true, results };
    } catch (error) {
      console.error('Error sharing property:', error);
      return { success: false, results: {} };
    }
  }

  // Share on specific platform
  private async shareOnPlatform(platform: SocialPlatform, content: ShareContent): Promise<any> {
    switch (platform.id) {
      case 'facebook':
        return this.shareOnFacebook(content);
      case 'twitter':
        return this.shareOnTwitter(content);
      case 'instagram':
        return this.shareOnInstagram(content);
      case 'linkedin':
        return this.shareOnLinkedIn(content);
      case 'whatsapp':
        return this.shareOnWhatsApp(content);
      case 'telegram':
        return this.shareOnTelegram(content);
      default:
        return this.generateShareUrl(platform, content);
    }
  }

  // Facebook sharing
  private async shareOnFacebook(content: ShareContent): Promise<any> {
    const shareUrl = new URL(this.platforms.find(p => p.id === 'facebook')!.shareUrl);
    shareUrl.searchParams.set('u', content.url);
    shareUrl.searchParams.set('quote', `${content.title}\n\n${content.description}`);

    if (typeof window !== 'undefined') {
      window.open(shareUrl.toString(), '_blank', 'width=600,height=400');
    }

    return { shareUrl: shareUrl.toString() };
  }

  // Twitter sharing
  private async shareOnTwitter(content: ShareContent): Promise<any> {
    const shareUrl = new URL(this.platforms.find(p => p.id === 'twitter')!.shareUrl);
    
    let tweetText = `${content.title}\n\n${content.description}`;
    if (content.price) {
      tweetText += `\n💰 ₦${content.price.toLocaleString()}`;
    }
    if (content.location) {
      tweetText += `\n📍 ${content.location}`;
    }
    if (content.hashtags) {
      tweetText += `\n\n${content.hashtags.map(tag => `#${tag}`).join(' ')}`;
    }

    shareUrl.searchParams.set('text', tweetText);
    shareUrl.searchParams.set('url', content.url);

    if (typeof window !== 'undefined') {
      window.open(shareUrl.toString(), '_blank', 'width=600,height=400');
    }

    return { shareUrl: shareUrl.toString() };
  }

  // Instagram sharing (opens Instagram app or web)
  private async shareOnInstagram(content: ShareContent): Promise<any> {
    // Instagram doesn't support direct URL sharing, so we'll copy content to clipboard
    const instagramText = `${content.title}\n\n${content.description}\n\n${content.url}`;
    
    if (typeof window !== 'undefined' && navigator.clipboard) {
      await navigator.clipboard.writeText(instagramText);
      alert('Content copied to clipboard! You can now paste it in Instagram.');
    }

    return { message: 'Content copied to clipboard for Instagram sharing' };
  }

  // LinkedIn sharing
  private async shareOnLinkedIn(content: ShareContent): Promise<any> {
    const shareUrl = new URL(this.platforms.find(p => p.id === 'linkedin')!.shareUrl);
    shareUrl.searchParams.set('url', content.url);
    shareUrl.searchParams.set('title', content.title);
    shareUrl.searchParams.set('summary', content.description);

    if (typeof window !== 'undefined') {
      window.open(shareUrl.toString(), '_blank', 'width=600,height=400');
    }

    return { shareUrl: shareUrl.toString() };
  }

  // WhatsApp sharing
  private async shareOnWhatsApp(content: ShareContent): Promise<any> {
    let whatsappText = `*${content.title}*\n\n${content.description}`;
    
    if (content.price) {
      whatsappText += `\n\n💰 *Price:* ₦${content.price.toLocaleString()}`;
    }
    if (content.location) {
      whatsappText += `\n📍 *Location:* ${content.location}`;
    }
    
    whatsappText += `\n\n🔗 ${content.url}`;

    const shareUrl = `${this.platforms.find(p => p.id === 'whatsapp')!.shareUrl}/?text=${encodeURIComponent(whatsappText)}`;

    if (typeof window !== 'undefined') {
      window.open(shareUrl, '_blank');
    }

    return { shareUrl };
  }

  // Telegram sharing
  private async shareOnTelegram(content: ShareContent): Promise<any> {
    const shareUrl = new URL(this.platforms.find(p => p.id === 'telegram')!.shareUrl);
    shareUrl.searchParams.set('url', content.url);
    shareUrl.searchParams.set('text', `${content.title}\n\n${content.description}`);

    if (typeof window !== 'undefined') {
      window.open(shareUrl.toString(), '_blank', 'width=600,height=400');
    }

    return { shareUrl: shareUrl.toString() };
  }

  // Generate generic share URL
  private generateShareUrl(platform: SocialPlatform, content: ShareContent): any {
    return {
      platform: platform.name,
      shareUrl: platform.shareUrl,
      content: content
    };
  }

  // Social login integration
  async initializeSocialLogin(provider: SocialLoginProvider): Promise<boolean> {
    try {
      switch (provider.provider) {
        case 'google':
          return this.initializeGoogleLogin(provider);
        case 'facebook':
          return this.initializeFacebookLogin(provider);
        case 'twitter':
          return this.initializeTwitterLogin(provider);
        case 'linkedin':
          return this.initializeLinkedInLogin(provider);
        default:
          throw new Error(`Unsupported provider: ${provider.provider}`);
      }
    } catch (error) {
      console.error('Error initializing social login:', error);
      return false;
    }
  }

  // Google login initialization
  private async initializeGoogleLogin(provider: SocialLoginProvider): Promise<boolean> {
    try {
      // Load Google Identity Services
      if (typeof window !== 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://accounts.google.com/gsi/client';
        script.async = true;
        document.head.appendChild(script);

        return new Promise((resolve) => {
          script.onload = () => {
            (window as any).google.accounts.id.initialize({
              client_id: provider.clientId,
              callback: this.handleGoogleLogin.bind(this)
            });
            resolve(true);
          };
        });
      }
      return false;
    } catch (error) {
      console.error('Google login initialization error:', error);
      return false;
    }
  }

  // Facebook login initialization
  private async initializeFacebookLogin(provider: SocialLoginProvider): Promise<boolean> {
    try {
      if (typeof window !== 'undefined') {
        // Load Facebook SDK
        (window as any).fbAsyncInit = () => {
          (window as any).FB.init({
            appId: provider.clientId,
            cookie: true,
            xfbml: true,
            version: 'v18.0'
          });
        };

        const script = document.createElement('script');
        script.src = 'https://connect.facebook.net/en_US/sdk.js';
        script.async = true;
        document.head.appendChild(script);

        return true;
      }
      return false;
    } catch (error) {
      console.error('Facebook login initialization error:', error);
      return false;
    }
  }

  // Twitter login initialization
  private async initializeTwitterLogin(provider: SocialLoginProvider): Promise<boolean> {
    // Twitter login would typically use OAuth 2.0 flow
    return true;
  }

  // LinkedIn login initialization
  private async initializeLinkedInLogin(provider: SocialLoginProvider): Promise<boolean> {
    // LinkedIn login would typically use OAuth 2.0 flow
    return true;
  }

  // Handle Google login callback
  private async handleGoogleLogin(response: any): Promise<void> {
    try {
      // Verify the JWT token with your backend
      const { data, error } = await supabase.auth.signInWithIdToken({
        provider: 'google',
        token: response.credential
      });

      if (error) throw error;

      // Store user social profile
      await this.storeSocialProfile(data.user, 'google', response);
    } catch (error) {
      console.error('Google login error:', error);
    }
  }

  // Store social profile information
  private async storeSocialProfile(user: any, provider: string, profileData: any): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_social_profiles')
        .upsert({
          user_id: user.id,
          provider,
          provider_id: profileData.sub || profileData.id,
          profile_data: profileData,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error storing social profile:', error);
    }
  }

  // Manage user-generated content
  async submitUserContent(content: Omit<UserGeneratedContent, 'id' | 'createdAt' | 'likes' | 'shares' | 'comments' | 'isVerified'>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_generated_content')
        .insert({
          ...content,
          likes: 0,
          shares: 0,
          comments: 0,
          is_verified: false,
          created_at: new Date().toISOString()
        });

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error submitting user content:', error);
      return false;
    }
  }

  // Get user-generated content for a property
  async getPropertyUserContent(propertyId: string, contentType?: string): Promise<UserGeneratedContent[]> {
    try {
      let query = supabase
        .from('user_generated_content')
        .select(`
          *,
          profiles:user_id (
            full_name,
            avatar_url
          )
        `)
        .eq('property_id', propertyId)
        .eq('is_verified', true)
        .order('created_at', { ascending: false });

      if (contentType) {
        query = query.eq('content_type', contentType);
      }

      const { data, error } = await query;

      if (error) throw error;

      return data?.map(item => ({
        id: item.id,
        userId: item.user_id,
        propertyId: item.property_id,
        platform: item.platform,
        contentType: item.content_type,
        content: item.content,
        mediaUrls: item.media_urls || [],
        rating: item.rating,
        likes: item.likes,
        shares: item.shares,
        comments: item.comments,
        isVerified: item.is_verified,
        createdAt: item.created_at
      })) || [];
    } catch (error) {
      console.error('Error getting user content:', error);
      return [];
    }
  }

  // Create social media campaign
  async createSocialCampaign(campaign: Omit<SocialCampaign, 'id' | 'metrics'>): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('social_campaigns')
        .insert({
          name: campaign.name,
          description: campaign.description,
          platforms: campaign.platforms,
          content: campaign.content,
          schedule: campaign.schedule,
          targeting: campaign.targeting,
          budget: campaign.budget,
          status: campaign.status
        })
        .select('id')
        .single();

      if (error) throw error;
      return data.id;
    } catch (error) {
      console.error('Error creating social campaign:', error);
      return null;
    }
  }

  // Get social media metrics
  async getSocialMetrics(propertyId?: string, dateRange?: { start: string; end: string }): Promise<SocialMetrics[]> {
    try {
      let query = supabase
        .from('social_sharing_logs')
        .select('platform, shares, likes, comments, reach, engagement, clicks');

      if (propertyId) {
        query = query.eq('property_id', propertyId);
      }

      if (dateRange) {
        query = query
          .gte('created_at', dateRange.start)
          .lte('created_at', dateRange.end);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Aggregate metrics by platform
      const metricsMap = new Map<string, SocialMetrics>();

      data?.forEach(log => {
        const existing = metricsMap.get(log.platform) || {
          platform: log.platform,
          shares: 0,
          likes: 0,
          comments: 0,
          reach: 0,
          engagement: 0,
          clicks: 0
        };

        existing.shares += log.shares || 0;
        existing.likes += log.likes || 0;
        existing.comments += log.comments || 0;
        existing.reach += log.reach || 0;
        existing.engagement += log.engagement || 0;
        existing.clicks += log.clicks || 0;

        metricsMap.set(log.platform, existing);
      });

      return Array.from(metricsMap.values());
    } catch (error) {
      console.error('Error getting social metrics:', error);
      return [];
    }
  }

  // Log sharing activity
  private async logSharingActivity(content: ShareContent, platforms: string[], results: Record<string, any>): Promise<void> {
    try {
      const logs = platforms.map(platform => ({
        platform,
        content_title: content.title,
        content_url: content.url,
        property_id: this.extractPropertyIdFromUrl(content.url),
        success: results[platform]?.success || false,
        error_message: results[platform]?.error || null,
        created_at: new Date().toISOString()
      }));

      const { error } = await supabase
        .from('social_sharing_logs')
        .insert(logs);

      if (error) throw error;
    } catch (error) {
      console.error('Error logging sharing activity:', error);
    }
  }

  // Extract property ID from URL
  private extractPropertyIdFromUrl(url: string): string | null {
    const match = url.match(/\/properties\/([^\/\?]+)/);
    return match ? match[1] : null;
  }

  // Get available platforms
  getPlatforms(): SocialPlatform[] {
    return this.platforms;
  }

  // Check if platform is configured
  isPlatformConfigured(platformId: string): boolean {
    return !!this.apiKeys[platformId];
  }

  // Generate social media preview
  generateSocialPreview(content: ShareContent, platformId: string): any {
    const platform = this.platforms.find(p => p.id === platformId);
    if (!platform) return null;

    return {
      platform: platform.name,
      icon: platform.icon,
      color: platform.color,
      preview: this.formatContentForPlatform(content, platformId)
    };
  }

  // Format content for specific platform
  private formatContentForPlatform(content: ShareContent, platformId: string): string {
    switch (platformId) {
      case 'twitter':
        let tweetText = `${content.title}\n\n${content.description}`;
        if (content.price) tweetText += `\n💰 ₦${content.price.toLocaleString()}`;
        if (content.location) tweetText += `\n📍 ${content.location}`;
        if (content.hashtags) tweetText += `\n\n${content.hashtags.map(tag => `#${tag}`).join(' ')}`;
        return tweetText;

      case 'facebook':
      case 'linkedin':
        return `${content.title}\n\n${content.description}`;

      case 'whatsapp':
        let whatsappText = `*${content.title}*\n\n${content.description}`;
        if (content.price) whatsappText += `\n\n💰 *Price:* ₦${content.price.toLocaleString()}`;
        if (content.location) whatsappText += `\n📍 *Location:* ${content.location}`;
        return whatsappText;

      default:
        return `${content.title}\n\n${content.description}`;
    }
  }
}

// Export singleton instance
export const socialMediaService = new SocialMediaService();
export default socialMediaService;
