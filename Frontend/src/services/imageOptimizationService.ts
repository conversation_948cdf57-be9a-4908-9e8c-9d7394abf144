// =====================================================
// IMAGE OPTIMIZATION & CDN SERVICE
// Advanced image processing and delivery optimization
// =====================================================

import { supabase } from '@/lib/supabase';

// Types and Interfaces
export interface ImageOptimizationConfig {
  quality: number;
  format: 'webp' | 'avif' | 'jpeg' | 'png' | 'auto';
  width?: number;
  height?: number;
  fit: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  progressive: boolean;
  lossless: boolean;
  stripMetadata: boolean;
}

export interface ResponsiveImageConfig {
  breakpoints: number[];
  formats: string[];
  quality: number;
  lazyLoading: boolean;
  placeholder: 'blur' | 'empty' | 'color';
  placeholderColor?: string;
}

export interface CDNConfig {
  provider: 'cloudinary' | 'imagekit' | 'cloudflare' | 'aws_cloudfront';
  baseUrl: string;
  apiKey: string;
  apiSecret?: string;
  transformations: Record<string, any>;
}

export interface OptimizedImage {
  originalUrl: string;
  optimizedUrl: string;
  cdnUrl?: string;
  formats: {
    webp?: string;
    avif?: string;
    jpeg?: string;
    png?: string;
  };
  sizes: {
    original: { width: number; height: number; size: number };
    optimized: { width: number; height: number; size: number };
  };
  metadata: {
    compressionRatio: number;
    processingTime: number;
    format: string;
  };
}

export interface LazyLoadConfig {
  rootMargin: string;
  threshold: number;
  fadeInDuration: number;
  placeholderQuality: number;
}

class ImageOptimizationService {
  private cdnConfig: CDNConfig | null = null;
  private defaultConfig: ImageOptimizationConfig = {
    quality: 85,
    format: 'auto',
    fit: 'cover',
    progressive: true,
    lossless: false,
    stripMetadata: true
  };

  private responsiveConfig: ResponsiveImageConfig = {
    breakpoints: [320, 640, 768, 1024, 1280, 1920],
    formats: ['avif', 'webp', 'jpeg'],
    quality: 85,
    lazyLoading: true,
    placeholder: 'blur'
  };

  constructor() {
    this.initializeCDN();
  }

  // Initialize CDN configuration
  private async initializeCDN(): Promise<void> {
    try {
      const provider = process.env.NEXT_PUBLIC_CDN_PROVIDER as CDNConfig['provider'];
      const baseUrl = process.env.NEXT_PUBLIC_CDN_BASE_URL;
      const apiKey = process.env.NEXT_PUBLIC_CDN_API_KEY;
      const apiSecret = process.env.CDN_API_SECRET;

      if (provider && baseUrl && apiKey) {
        this.cdnConfig = {
          provider,
          baseUrl,
          apiKey,
          apiSecret,
          transformations: this.getDefaultTransformations(provider)
        };
      }
    } catch (error) {
      console.warn('CDN initialization failed:', error);
    }
  }

  // Get default transformations for CDN provider
  private getDefaultTransformations(provider: CDNConfig['provider']): Record<string, any> {
    switch (provider) {
      case 'cloudinary':
        return {
          quality: 'auto:good',
          fetch_format: 'auto',
          flags: 'progressive',
          crop: 'fill',
          gravity: 'auto'
        };
      case 'imagekit':
        return {
          quality: 85,
          format: 'auto',
          progressive: true,
          crop: 'maintain_ratio'
        };
      case 'cloudflare':
        return {
          quality: 85,
          format: 'auto',
          fit: 'cover'
        };
      default:
        return {};
    }
  }

  // Optimize single image
  async optimizeImage(
    imageUrl: string, 
    config: Partial<ImageOptimizationConfig> = {}
  ): Promise<OptimizedImage | null> {
    try {
      const startTime = Date.now();
      const optimizationConfig = { ...this.defaultConfig, ...config };

      // Get original image metadata
      const originalMetadata = await this.getImageMetadata(imageUrl);
      if (!originalMetadata) {
        throw new Error('Failed to get image metadata');
      }

      let optimizedUrl: string;
      let cdnUrl: string | undefined;

      if (this.cdnConfig) {
        // Use CDN for optimization
        const result = await this.optimizeWithCDN(imageUrl, optimizationConfig);
        optimizedUrl = result.optimizedUrl;
        cdnUrl = result.cdnUrl;
      } else {
        // Use local optimization
        optimizedUrl = await this.optimizeLocally(imageUrl, optimizationConfig);
      }

      // Get optimized image metadata
      const optimizedMetadata = await this.getImageMetadata(optimizedUrl);
      if (!optimizedMetadata) {
        throw new Error('Failed to get optimized image metadata');
      }

      const processingTime = Date.now() - startTime;
      const compressionRatio = ((originalMetadata.size - optimizedMetadata.size) / originalMetadata.size) * 100;

      // Generate multiple formats
      const formats = await this.generateMultipleFormats(optimizedUrl, optimizationConfig);

      const result: OptimizedImage = {
        originalUrl: imageUrl,
        optimizedUrl,
        cdnUrl,
        formats,
        sizes: {
          original: originalMetadata,
          optimized: optimizedMetadata
        },
        metadata: {
          compressionRatio,
          processingTime,
          format: optimizationConfig.format
        }
      };

      // Log optimization results
      await this.logOptimization(result);

      return result;
    } catch (error) {
      console.error('Image optimization failed:', error);
      return null;
    }
  }

  // Optimize with CDN
  private async optimizeWithCDN(
    imageUrl: string, 
    config: ImageOptimizationConfig
  ): Promise<{ optimizedUrl: string; cdnUrl: string }> {
    if (!this.cdnConfig) {
      throw new Error('CDN not configured');
    }

    switch (this.cdnConfig.provider) {
      case 'cloudinary':
        return this.optimizeWithCloudinary(imageUrl, config);
      case 'imagekit':
        return this.optimizeWithImageKit(imageUrl, config);
      case 'cloudflare':
        return this.optimizeWithCloudflare(imageUrl, config);
      default:
        throw new Error(`Unsupported CDN provider: ${this.cdnConfig.provider}`);
    }
  }

  // Cloudinary optimization
  private async optimizeWithCloudinary(
    imageUrl: string, 
    config: ImageOptimizationConfig
  ): Promise<{ optimizedUrl: string; cdnUrl: string }> {
    const transformations = [
      `q_${config.quality}`,
      `f_${config.format === 'auto' ? 'auto' : config.format}`,
      config.width ? `w_${config.width}` : '',
      config.height ? `h_${config.height}` : '',
      `c_${config.fit}`,
      config.progressive ? 'fl_progressive' : '',
      config.stripMetadata ? 'fl_strip_profile' : ''
    ].filter(Boolean).join(',');

    const cdnUrl = `${this.cdnConfig!.baseUrl}/image/fetch/${transformations}/${encodeURIComponent(imageUrl)}`;
    
    return {
      optimizedUrl: cdnUrl,
      cdnUrl
    };
  }

  // ImageKit optimization
  private async optimizeWithImageKit(
    imageUrl: string, 
    config: ImageOptimizationConfig
  ): Promise<{ optimizedUrl: string; cdnUrl: string }> {
    const transformations = [
      `q-${config.quality}`,
      config.format !== 'auto' ? `f-${config.format}` : '',
      config.width ? `w-${config.width}` : '',
      config.height ? `h-${config.height}` : '',
      `c-${config.fit}`,
      config.progressive ? 'pr-true' : ''
    ].filter(Boolean).join(',');

    const cdnUrl = `${this.cdnConfig!.baseUrl}/tr:${transformations}/${encodeURIComponent(imageUrl)}`;
    
    return {
      optimizedUrl: cdnUrl,
      cdnUrl
    };
  }

  // Cloudflare optimization
  private async optimizeWithCloudflare(
    imageUrl: string, 
    config: ImageOptimizationConfig
  ): Promise<{ optimizedUrl: string; cdnUrl: string }> {
    const params = new URLSearchParams({
      quality: config.quality.toString(),
      format: config.format === 'auto' ? 'auto' : config.format,
      fit: config.fit
    });

    if (config.width) params.set('width', config.width.toString());
    if (config.height) params.set('height', config.height.toString());

    const cdnUrl = `${this.cdnConfig!.baseUrl}/cdn-cgi/image/${params.toString()}/${imageUrl}`;
    
    return {
      optimizedUrl: cdnUrl,
      cdnUrl
    };
  }

  // Local optimization (fallback)
  private async optimizeLocally(
    imageUrl: string, 
    config: ImageOptimizationConfig
  ): Promise<string> {
    // This would integrate with a local image processing library
    // For now, return the original URL as fallback
    console.warn('Local image optimization not implemented, using original URL');
    return imageUrl;
  }

  // Generate multiple formats
  private async generateMultipleFormats(
    baseUrl: string, 
    config: ImageOptimizationConfig
  ): Promise<OptimizedImage['formats']> {
    const formats: OptimizedImage['formats'] = {};

    for (const format of this.responsiveConfig.formats) {
      try {
        if (this.cdnConfig) {
          const formatConfig = { ...config, format: format as any };
          const result = await this.optimizeWithCDN(baseUrl, formatConfig);
          formats[format as keyof OptimizedImage['formats']] = result.optimizedUrl;
        }
      } catch (error) {
        console.warn(`Failed to generate ${format} format:`, error);
      }
    }

    return formats;
  }

  // Generate responsive images
  async generateResponsiveImages(
    imageUrl: string,
    config: Partial<ResponsiveImageConfig> = {}
  ): Promise<{
    srcSet: string;
    sizes: string;
    placeholder: string;
    formats: Record<string, string>;
  }> {
    const responsiveConfig = { ...this.responsiveConfig, ...config };
    const srcSets: Record<string, string[]> = {};
    const formats: Record<string, string> = {};

    // Generate images for each breakpoint and format
    for (const format of responsiveConfig.formats) {
      srcSets[format] = [];
      
      for (const width of responsiveConfig.breakpoints) {
        try {
          const optimized = await this.optimizeImage(imageUrl, {
            format: format as any,
            width,
            quality: responsiveConfig.quality
          });

          if (optimized) {
            const url = optimized.formats[format as keyof OptimizedImage['formats']] || optimized.optimizedUrl;
            srcSets[format].push(`${url} ${width}w`);
            
            if (!formats[format]) {
              formats[format] = url;
            }
          }
        } catch (error) {
          console.warn(`Failed to generate responsive image for ${format} at ${width}px:`, error);
        }
      }
    }

    // Generate placeholder
    const placeholder = await this.generatePlaceholder(imageUrl, responsiveConfig);

    // Create sizes attribute
    const sizes = responsiveConfig.breakpoints
      .map((bp, index) => {
        if (index === responsiveConfig.breakpoints.length - 1) {
          return `${bp}px`;
        }
        return `(max-width: ${bp}px) ${bp}px`;
      })
      .join(', ');

    return {
      srcSet: srcSets.webp?.join(', ') || srcSets.jpeg?.join(', ') || '',
      sizes,
      placeholder,
      formats
    };
  }

  // Generate placeholder image
  private async generatePlaceholder(
    imageUrl: string,
    config: ResponsiveImageConfig
  ): Promise<string> {
    try {
      if (config.placeholder === 'blur') {
        // Generate low-quality placeholder
        const placeholder = await this.optimizeImage(imageUrl, {
          width: 20,
          height: 20,
          quality: config.placeholderQuality || 20,
          format: 'jpeg'
        });
        return placeholder?.optimizedUrl || '';
      } else if (config.placeholder === 'color') {
        // Generate solid color placeholder
        return `data:image/svg+xml;base64,${btoa(
          `<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="${config.placeholderColor || '#f3f4f6'}"/>
          </svg>`
        )}`;
      }
      return '';
    } catch (error) {
      console.warn('Failed to generate placeholder:', error);
      return '';
    }
  }

  // Get image metadata
  private async getImageMetadata(imageUrl: string): Promise<{
    width: number;
    height: number;
    size: number;
  } | null> {
    try {
      // This would typically use a library like sharp or canvas
      // For now, we'll simulate metadata
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.naturalWidth,
            height: img.naturalHeight,
            size: 0 // Would need to fetch actual file size
          });
        };
        img.onerror = () => resolve(null);
        img.src = imageUrl;
      });
    } catch (error) {
      console.error('Failed to get image metadata:', error);
      return null;
    }
  }

  // Implement lazy loading
  createLazyLoader(config: Partial<LazyLoadConfig> = {}): IntersectionObserver {
    const lazyConfig: LazyLoadConfig = {
      rootMargin: '50px',
      threshold: 0.1,
      fadeInDuration: 300,
      placeholderQuality: 20,
      ...config
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          this.loadImage(img, lazyConfig);
          observer.unobserve(img);
        }
      });
    }, {
      rootMargin: lazyConfig.rootMargin,
      threshold: lazyConfig.threshold
    });

    return observer;
  }

  // Load image with fade-in effect
  private async loadImage(img: HTMLImageElement, config: LazyLoadConfig): Promise<void> {
    const src = img.dataset.src;
    const srcSet = img.dataset.srcset;

    if (!src) return;

    // Create new image to preload
    const newImg = new Image();
    
    return new Promise((resolve) => {
      newImg.onload = () => {
        // Apply loaded image
        img.src = src;
        if (srcSet) img.srcset = srcSet;
        
        // Fade in effect
        img.style.opacity = '0';
        img.style.transition = `opacity ${config.fadeInDuration}ms ease-in-out`;
        
        requestAnimationFrame(() => {
          img.style.opacity = '1';
        });
        
        resolve();
      };
      
      newImg.onerror = () => {
        console.warn('Failed to load image:', src);
        resolve();
      };
      
      newImg.src = src;
      if (srcSet) newImg.srcset = srcSet;
    });
  }

  // Batch optimize images
  async batchOptimize(
    imageUrls: string[],
    config: Partial<ImageOptimizationConfig> = {},
    onProgress?: (completed: number, total: number) => void
  ): Promise<OptimizedImage[]> {
    const results: OptimizedImage[] = [];
    const batchSize = 5; // Process 5 images at a time

    for (let i = 0; i < imageUrls.length; i += batchSize) {
      const batch = imageUrls.slice(i, i + batchSize);
      
      const batchPromises = batch.map(url => this.optimizeImage(url, config));
      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result) => {
        if (result.status === 'fulfilled' && result.value) {
          results.push(result.value);
        }
      });
      
      if (onProgress) {
        onProgress(Math.min(i + batchSize, imageUrls.length), imageUrls.length);
      }
    }

    return results;
  }

  // Preload critical images
  async preloadCriticalImages(imageUrls: string[]): Promise<void> {
    const preloadPromises = imageUrls.map(url => {
      return new Promise<void>((resolve) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = url;
        link.onload = () => resolve();
        link.onerror = () => resolve();
        document.head.appendChild(link);
      });
    });

    await Promise.all(preloadPromises);
  }

  // Get optimization statistics
  async getOptimizationStats(timeRange: 'day' | 'week' | 'month' = 'day'): Promise<{
    totalImages: number;
    totalSavings: number;
    averageCompression: number;
    formatDistribution: Record<string, number>;
    processingTime: number;
  }> {
    try {
      const interval = timeRange === 'day' ? '1 day' : timeRange === 'week' ? '7 days' : '30 days';
      
      const { data, error } = await supabase
        .from('image_optimization_logs')
        .select('*')
        .gte('created_at', new Date(Date.now() - (timeRange === 'day' ? 86400000 : timeRange === 'week' ? 604800000 : 2592000000)).toISOString());

      if (error) throw error;

      const stats = data?.reduce((acc, log) => {
        acc.totalImages++;
        acc.totalSavings += log.original_size_bytes - log.optimized_size_bytes;
        acc.averageCompression += log.compression_ratio;
        acc.processingTime += log.processing_time_ms;
        
        acc.formatDistribution[log.format_optimized] = (acc.formatDistribution[log.format_optimized] || 0) + 1;
        
        return acc;
      }, {
        totalImages: 0,
        totalSavings: 0,
        averageCompression: 0,
        formatDistribution: {} as Record<string, number>,
        processingTime: 0
      });

      if (stats && stats.totalImages > 0) {
        stats.averageCompression /= stats.totalImages;
        stats.processingTime /= stats.totalImages;
      }

      return stats || {
        totalImages: 0,
        totalSavings: 0,
        averageCompression: 0,
        formatDistribution: {},
        processingTime: 0
      };
    } catch (error) {
      console.error('Failed to get optimization stats:', error);
      return {
        totalImages: 0,
        totalSavings: 0,
        averageCompression: 0,
        formatDistribution: {},
        processingTime: 0
      };
    }
  }

  // Log optimization results
  private async logOptimization(result: OptimizedImage): Promise<void> {
    try {
      await supabase
        .from('image_optimization_logs')
        .insert({
          original_url: result.originalUrl,
          optimized_url: result.optimizedUrl,
          original_size_bytes: result.sizes.original.size,
          optimized_size_bytes: result.sizes.optimized.size,
          format_original: 'jpeg', // Would detect from original
          format_optimized: result.metadata.format,
          optimization_type: 'compression',
          processing_time_ms: result.metadata.processingTime,
          cdn_url: result.cdnUrl
        });
    } catch (error) {
      console.error('Failed to log optimization:', error);
    }
  }

  // Clean up old optimization logs
  async cleanupLogs(daysToKeep: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date(Date.now() - (daysToKeep * 24 * 60 * 60 * 1000));
      
      await supabase
        .from('image_optimization_logs')
        .delete()
        .lt('created_at', cutoffDate.toISOString());
    } catch (error) {
      console.error('Failed to cleanup optimization logs:', error);
    }
  }

  // Check if browser supports format
  static supportsFormat(format: string): boolean {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    try {
      return canvas.toDataURL(`image/${format}`).indexOf(`data:image/${format}`) === 0;
    } catch {
      return false;
    }
  }

  // Get optimal format for browser
  static getOptimalFormat(): string {
    if (this.supportsFormat('avif')) return 'avif';
    if (this.supportsFormat('webp')) return 'webp';
    return 'jpeg';
  }
}

// Export singleton instance
export const imageOptimizationService = new ImageOptimizationService();
export default imageOptimizationService;
