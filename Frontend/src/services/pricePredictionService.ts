import { supabase } from '@/integrations/supabase/client';

export interface PropertyFeatures {
  location: string;
  property_type: string;
  bedrooms: number;
  bathrooms: number;
  area_sqft: number;
  age_years: number;
  condition_score: number;
  amenities: string[];
  neighborhood_score: number;
  transport_accessibility: number;
  safety_score: number;
  market_demand: number;
}

export interface PricePrediction {
  predicted_price: number;
  confidence_score: number;
  price_range: {
    min: number;
    max: number;
  };
  factors: {
    location_impact: number;
    size_impact: number;
    condition_impact: number;
    market_impact: number;
    amenity_impact: number;
  };
  comparable_properties: Array<{
    property_id: string;
    price: number;
    similarity_score: number;
    location: string;
  }>;
  market_trends: {
    trend_direction: 'rising' | 'falling' | 'stable';
    trend_strength: number;
    seasonal_factor: number;
  };
  recommendation: string;
  generated_at: string;
}

export interface MarketTrend {
  location: string;
  property_type: string;
  time_period: string;
  average_price: number;
  price_change_percentage: number;
  volume_change_percentage: number;
  demand_score: number;
  supply_score: number;
}

export interface PriceHistory {
  property_id: string;
  price: number;
  date: string;
  event_type: 'listing' | 'price_change' | 'rental' | 'market_update';
  source: string;
}

/**
 * AI-Powered Price Prediction Service
 * Uses machine learning algorithms to predict property prices
 * based on historical data, market trends, and property features
 */
export class PricePredictionService {
  private static instance: PricePredictionService;
  private priceModelCache: Map<string, any> = new Map();
  private marketTrendCache: Map<string, MarketTrend[]> = new Map();
  private cacheExpiry: number = 60 * 60 * 1000; // 1 hour

  public static getInstance(): PricePredictionService {
    if (!PricePredictionService.instance) {
      PricePredictionService.instance = new PricePredictionService();
    }
    return PricePredictionService.instance;
  }

  // =====================================================
  // MAIN PRICE PREDICTION ENGINE
  // =====================================================

  /**
   * Predict property price using multiple algorithms
   */
  async predictPrice(propertyFeatures: PropertyFeatures): Promise<PricePrediction> {
    try {
      // Get comparable properties
      const comparableProperties = await this.findComparableProperties(propertyFeatures);
      
      // Calculate base price using comparable properties
      const basePrice = this.calculateBasePriceFromComparables(comparableProperties);
      
      // Apply feature adjustments
      const featureAdjustedPrice = await this.applyFeatureAdjustments(basePrice, propertyFeatures);
      
      // Apply market trend adjustments
      const marketAdjustedPrice = await this.applyMarketTrendAdjustments(
        featureAdjustedPrice, 
        propertyFeatures.location, 
        propertyFeatures.property_type
      );
      
      // Calculate confidence score
      const confidenceScore = this.calculateConfidenceScore(
        comparableProperties, 
        propertyFeatures
      );
      
      // Calculate price range
      const priceRange = this.calculatePriceRange(marketAdjustedPrice, confidenceScore);
      
      // Analyze impact factors
      const factors = await this.analyzeImpactFactors(propertyFeatures, basePrice, marketAdjustedPrice);
      
      // Get market trends
      const marketTrends = await this.getMarketTrends(
        propertyFeatures.location, 
        propertyFeatures.property_type
      );
      
      // Generate recommendation
      const recommendation = this.generatePriceRecommendation(
        marketAdjustedPrice, 
        marketTrends, 
        confidenceScore
      );

      return {
        predicted_price: Math.round(marketAdjustedPrice),
        confidence_score: Math.round(confidenceScore * 100) / 100,
        price_range: {
          min: Math.round(priceRange.min),
          max: Math.round(priceRange.max)
        },
        factors,
        comparable_properties: comparableProperties.slice(0, 5),
        market_trends: marketTrends,
        recommendation,
        generated_at: new Date().toISOString()
      };

    } catch (error) {
      console.error('Error predicting price:', error);
      throw new Error('Failed to predict property price');
    }
  }

  /**
   * Get historical price trends for a location
   */
  async getHistoricalTrends(
    location: string, 
    propertyType?: string, 
    months: number = 12
  ): Promise<MarketTrend[]> {
    try {
      const cacheKey = `${location}-${propertyType}-${months}`;
      const cached = this.marketTrendCache.get(cacheKey);
      
      if (cached) return cached;

      const { data, error } = await supabase.rpc('get_price_trends', {
        location_filter: location,
        property_type_filter: propertyType,
        months_back: months
      });

      if (error) throw error;

      const trends = data || [];
      this.marketTrendCache.set(cacheKey, trends);
      
      return trends;
    } catch (error) {
      console.error('Error getting historical trends:', error);
      return [];
    }
  }

  /**
   * Analyze price factors for a specific property
   */
  async analyzePriceFactors(propertyId: string): Promise<{
    current_price: number;
    market_position: 'below' | 'at' | 'above';
    price_per_sqft: number;
    location_premium: number;
    feature_premium: number;
    suggestions: string[];
  }> {
    try {
      // Get property details
      const { data: property, error } = await supabase
        .from('properties')
        .select('*')
        .eq('id', propertyId)
        .single();

      if (error) throw error;

      // Get market data for comparison
      const marketData = await this.getMarketData(property.location, property.property_type);
      
      // Calculate metrics
      const pricePerSqft = property.area_sqft > 0 ? property.price_per_year / property.area_sqft : 0;
      const marketPricePerSqft = marketData.average_price_per_sqft || 0;
      
      const locationPremium = marketPricePerSqft > 0 ? 
        ((pricePerSqft - marketPricePerSqft) / marketPricePerSqft) * 100 : 0;
      
      // Determine market position
      let marketPosition: 'below' | 'at' | 'above' = 'at';
      if (property.price_per_year < marketData.average_price * 0.95) {
        marketPosition = 'below';
      } else if (property.price_per_year > marketData.average_price * 1.05) {
        marketPosition = 'above';
      }

      // Generate suggestions
      const suggestions = this.generatePricingSuggestions(
        property, 
        marketData, 
        marketPosition
      );

      return {
        current_price: property.price_per_year,
        market_position: marketPosition,
        price_per_sqft: Math.round(pricePerSqft),
        location_premium: Math.round(locationPremium * 100) / 100,
        feature_premium: 0, // Calculate based on amenities and features
        suggestions
      };

    } catch (error) {
      console.error('Error analyzing price factors:', error);
      throw new Error('Failed to analyze price factors');
    }
  }

  // =====================================================
  // PRICE CALCULATION ALGORITHMS
  // =====================================================

  /**
   * Find comparable properties using similarity algorithm
   */
  private async findComparableProperties(
    propertyFeatures: PropertyFeatures
  ): Promise<Array<{
    property_id: string;
    price: number;
    similarity_score: number;
    location: string;
  }>> {
    try {
      const { data: properties, error } = await supabase
        .from('properties')
        .select('id, price_per_year, location, property_type, bedrooms, bathrooms, area_sqft, amenities')
        .eq('property_type', propertyFeatures.property_type)
        .gte('bedrooms', propertyFeatures.bedrooms - 1)
        .lte('bedrooms', propertyFeatures.bedrooms + 1)
        .gte('bathrooms', propertyFeatures.bathrooms - 1)
        .lte('bathrooms', propertyFeatures.bathrooms + 1)
        .not('price_per_year', 'is', null)
        .limit(50);

      if (error) throw error;

      // Calculate similarity scores
      const comparables = properties.map(property => ({
        property_id: property.id,
        price: property.price_per_year,
        location: property.location,
        similarity_score: this.calculateSimilarityScore(propertyFeatures, property)
      }));

      // Sort by similarity and return top matches
      return comparables
        .sort((a, b) => b.similarity_score - a.similarity_score)
        .slice(0, 10);

    } catch (error) {
      console.error('Error finding comparable properties:', error);
      return [];
    }
  }

  /**
   * Calculate similarity score between properties
   */
  private calculateSimilarityScore(target: PropertyFeatures, comparable: any): number {
    let score = 0;
    let weights = 0;

    // Location similarity (highest weight)
    const locationWeight = 0.4;
    const locationScore = this.calculateLocationSimilarity(target.location, comparable.location);
    score += locationScore * locationWeight;
    weights += locationWeight;

    // Size similarity
    const sizeWeight = 0.2;
    const sizeScore = this.calculateSizeSimilarity(target.area_sqft, comparable.area_sqft);
    score += sizeScore * sizeWeight;
    weights += sizeWeight;

    // Room configuration similarity
    const roomWeight = 0.2;
    const roomScore = this.calculateRoomSimilarity(target, comparable);
    score += roomScore * roomWeight;
    weights += roomWeight;

    // Amenity similarity
    const amenityWeight = 0.2;
    const amenityScore = this.calculateAmenitySimilarity(target.amenities, comparable.amenities);
    score += amenityScore * amenityWeight;
    weights += amenityWeight;

    return weights > 0 ? score / weights : 0;
  }

  /**
   * Calculate base price from comparable properties
   */
  private calculateBasePriceFromComparables(
    comparables: Array<{ price: number; similarity_score: number }>
  ): number {
    if (!comparables.length) return 0;

    // Weighted average based on similarity scores
    let totalWeightedPrice = 0;
    let totalWeight = 0;

    for (const comparable of comparables) {
      const weight = comparable.similarity_score;
      totalWeightedPrice += comparable.price * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? totalWeightedPrice / totalWeight : 
           comparables.reduce((sum, c) => sum + c.price, 0) / comparables.length;
  }

  /**
   * Apply feature-based price adjustments
   */
  private async applyFeatureAdjustments(
    basePrice: number, 
    features: PropertyFeatures
  ): Promise<number> {
    let adjustedPrice = basePrice;

    // Condition adjustment
    const conditionMultiplier = 0.8 + (features.condition_score * 0.4);
    adjustedPrice *= conditionMultiplier;

    // Age adjustment
    const ageAdjustment = Math.max(0.9, 1 - (features.age_years * 0.01));
    adjustedPrice *= ageAdjustment;

    // Amenity premium
    const amenityPremium = this.calculateAmenityPremium(features.amenities);
    adjustedPrice *= (1 + amenityPremium);

    // Accessibility and safety adjustments
    const accessibilityMultiplier = 0.9 + (features.transport_accessibility * 0.1);
    const safetyMultiplier = 0.95 + (features.safety_score * 0.05);
    
    adjustedPrice *= accessibilityMultiplier * safetyMultiplier;

    return adjustedPrice;
  }

  /**
   * Apply market trend adjustments
   */
  private async applyMarketTrendAdjustments(
    price: number, 
    location: string, 
    propertyType: string
  ): Promise<number> {
    try {
      const trends = await this.getMarketTrends(location, propertyType);
      
      let trendMultiplier = 1.0;
      
      if (trends.trend_direction === 'rising') {
        trendMultiplier = 1 + (trends.trend_strength * 0.1);
      } else if (trends.trend_direction === 'falling') {
        trendMultiplier = 1 - (trends.trend_strength * 0.1);
      }

      // Apply seasonal factor
      trendMultiplier *= (1 + trends.seasonal_factor);

      return price * trendMultiplier;
    } catch (error) {
      console.error('Error applying market trend adjustments:', error);
      return price;
    }
  }
}

// Export singleton instance
export const pricePredictionService = PricePredictionService.getInstance();
