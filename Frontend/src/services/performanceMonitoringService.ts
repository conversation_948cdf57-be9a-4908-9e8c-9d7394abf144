// =====================================================
// PERFORMANCE MONITORING SERVICE
// Real-time performance tracking and Core Web Vitals monitoring
// =====================================================

import { supabase } from '@/lib/supabase';

// Types and Interfaces
export interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  
  // Additional Metrics
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte
  
  // Custom Metrics
  pageLoadTime: number;
  domContentLoaded: number;
  resourceLoadTime: number;
  
  // User Context
  url: string;
  userAgent: string;
  connectionType: string;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  timestamp: number;
}

export interface PerformanceBudget {
  metric: string;
  budget: number;
  warning: number;
  critical: number;
  device: 'mobile' | 'desktop' | 'all';
}

export interface PerformanceAlert {
  id: string;
  type: 'budget_exceeded' | 'performance_degradation' | 'error_spike';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  metric: string;
  value: number;
  threshold: number;
  url: string;
  timestamp: number;
  acknowledged: boolean;
}

export interface PerformanceReport {
  period: string;
  metrics: {
    averageLCP: number;
    averageFID: number;
    averageCLS: number;
    performanceScore: number;
  };
  trends: {
    metric: string;
    trend: 'improving' | 'stable' | 'degrading';
    change: number;
  }[];
  recommendations: string[];
  budgetStatus: {
    metric: string;
    status: 'within' | 'warning' | 'exceeded';
    value: number;
    budget: number;
  }[];
}

class PerformanceMonitoringService {
  private observer: PerformanceObserver | null = null;
  private metrics: PerformanceMetrics | null = null;
  private budgets: PerformanceBudget[] = [];
  private alerts: PerformanceAlert[] = [];
  private isMonitoring = false;

  constructor() {
    this.initializeDefaultBudgets();
    this.startMonitoring();
  }

  // Initialize default performance budgets
  private initializeDefaultBudgets(): void {
    this.budgets = [
      { metric: 'lcp', budget: 2500, warning: 2000, critical: 4000, device: 'mobile' },
      { metric: 'lcp', budget: 2000, warning: 1500, critical: 3000, device: 'desktop' },
      { metric: 'fid', budget: 100, warning: 80, critical: 300, device: 'mobile' },
      { metric: 'fid', budget: 50, warning: 40, critical: 200, device: 'desktop' },
      { metric: 'cls', budget: 0.1, warning: 0.08, critical: 0.25, device: 'all' },
      { metric: 'fcp', budget: 1800, warning: 1500, critical: 3000, device: 'mobile' },
      { metric: 'fcp', budget: 1200, warning: 1000, critical: 2000, device: 'desktop' },
      { metric: 'ttfb', budget: 800, warning: 600, critical: 1800, device: 'all' }
    ];
  }

  // Start performance monitoring
  startMonitoring(): void {
    if (this.isMonitoring || typeof window === 'undefined') return;

    try {
      // Monitor Core Web Vitals
      this.monitorCoreWebVitals();
      
      // Monitor navigation timing
      this.monitorNavigationTiming();
      
      // Monitor resource timing
      this.monitorResourceTiming();
      
      // Monitor long tasks
      this.monitorLongTasks();
      
      // Monitor layout shifts
      this.monitorLayoutShifts();

      this.isMonitoring = true;
      console.log('Performance monitoring started');
    } catch (error) {
      console.error('Failed to start performance monitoring:', error);
    }
  }

  // Monitor Core Web Vitals
  private monitorCoreWebVitals(): void {
    // Largest Contentful Paint (LCP)
    this.observePerformanceEntries('largest-contentful-paint', (entries) => {
      const lastEntry = entries[entries.length - 1];
      this.updateMetric('lcp', lastEntry.startTime);
    });

    // First Input Delay (FID)
    this.observePerformanceEntries('first-input', (entries) => {
      const firstEntry = entries[0];
      this.updateMetric('fid', firstEntry.processingStart - firstEntry.startTime);
    });

    // Cumulative Layout Shift (CLS)
    let clsValue = 0;
    this.observePerformanceEntries('layout-shift', (entries) => {
      for (const entry of entries) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      }
      this.updateMetric('cls', clsValue);
    });

    // First Contentful Paint (FCP)
    this.observePerformanceEntries('paint', (entries) => {
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        this.updateMetric('fcp', fcpEntry.startTime);
      }
    });
  }

  // Monitor navigation timing
  private monitorNavigationTiming(): void {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        this.updateMetric('ttfb', navigation.responseStart - navigation.requestStart);
        this.updateMetric('pageLoadTime', navigation.loadEventEnd - navigation.fetchStart);
        this.updateMetric('domContentLoaded', navigation.domContentLoadedEventEnd - navigation.fetchStart);
      }
    });
  }

  // Monitor resource timing
  private monitorResourceTiming(): void {
    this.observePerformanceEntries('resource', (entries) => {
      const totalResourceTime = entries.reduce((total, entry) => {
        return total + (entry.responseEnd - entry.startTime);
      }, 0);
      
      this.updateMetric('resourceLoadTime', totalResourceTime);
    });
  }

  // Monitor long tasks
  private monitorLongTasks(): void {
    this.observePerformanceEntries('longtask', (entries) => {
      entries.forEach(entry => {
        console.warn(`Long task detected: ${entry.duration}ms`);
        this.createAlert({
          type: 'performance_degradation',
          severity: entry.duration > 100 ? 'high' : 'medium',
          message: `Long task detected: ${Math.round(entry.duration)}ms`,
          metric: 'longtask',
          value: entry.duration,
          threshold: 50,
          url: window.location.href
        });
      });
    });
  }

  // Monitor layout shifts
  private monitorLayoutShifts(): void {
    this.observePerformanceEntries('layout-shift', (entries) => {
      entries.forEach(entry => {
        if (entry.value > 0.1 && !entry.hadRecentInput) {
          console.warn(`Significant layout shift detected: ${entry.value}`);
        }
      });
    });
  }

  // Generic performance observer
  private observePerformanceEntries(
    entryType: string, 
    callback: (entries: PerformanceEntry[]) => void
  ): void {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });
      
      observer.observe({ entryTypes: [entryType] });
    } catch (error) {
      console.warn(`Failed to observe ${entryType}:`, error);
    }
  }

  // Update performance metric
  private updateMetric(metric: string, value: number): void {
    if (!this.metrics) {
      this.metrics = {
        lcp: 0,
        fid: 0,
        cls: 0,
        fcp: 0,
        ttfb: 0,
        pageLoadTime: 0,
        domContentLoaded: 0,
        resourceLoadTime: 0,
        url: window.location.href,
        userAgent: navigator.userAgent,
        connectionType: this.getConnectionType(),
        deviceType: this.getDeviceType(),
        timestamp: Date.now()
      };
    }

    (this.metrics as any)[metric] = value;

    // Check against budgets
    this.checkPerformanceBudgets(metric, value);

    // Send metrics to backend periodically
    this.throttledSendMetrics();
  }

  // Get connection type
  private getConnectionType(): string {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    return connection ? connection.effectiveType || 'unknown' : 'unknown';
  }

  // Get device type
  private getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    const width = window.innerWidth;
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  }

  // Check performance budgets
  private checkPerformanceBudgets(metric: string, value: number): void {
    const deviceType = this.getDeviceType();
    const budget = this.budgets.find(b => 
      b.metric === metric && (b.device === deviceType || b.device === 'all')
    );

    if (!budget) return;

    if (value > budget.critical) {
      this.createAlert({
        type: 'budget_exceeded',
        severity: 'critical',
        message: `${metric.toUpperCase()} exceeded critical threshold`,
        metric,
        value,
        threshold: budget.critical,
        url: window.location.href
      });
    } else if (value > budget.warning) {
      this.createAlert({
        type: 'budget_exceeded',
        severity: 'medium',
        message: `${metric.toUpperCase()} exceeded warning threshold`,
        metric,
        value,
        threshold: budget.warning,
        url: window.location.href
      });
    }
  }

  // Create performance alert
  private createAlert(alertData: Omit<PerformanceAlert, 'id' | 'timestamp' | 'acknowledged'>): void {
    const alert: PerformanceAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      acknowledged: false,
      ...alertData
    };

    this.alerts.push(alert);
    
    // Send alert to backend
    this.sendAlert(alert);
    
    // Trigger alert callback if configured
    this.onAlert?.(alert);
  }

  // Throttled metrics sending
  private throttledSendMetrics = this.throttle(() => {
    if (this.metrics) {
      this.sendMetrics(this.metrics);
    }
  }, 5000); // Send metrics every 5 seconds

  // Send metrics to backend
  private async sendMetrics(metrics: PerformanceMetrics): Promise<void> {
    try {
      await supabase
        .from('web_vitals_metrics')
        .insert({
          user_id: null, // Would get from auth context
          session_id: this.getSessionId(),
          page_url: metrics.url,
          device_type: metrics.deviceType,
          connection_type: metrics.connectionType,
          lcp_value: metrics.lcp,
          fid_value: metrics.fid,
          cls_value: metrics.cls,
          fcp_value: metrics.fcp,
          ttfb_value: metrics.ttfb,
          performance_score: this.calculatePerformanceScore(metrics)
        });
    } catch (error) {
      console.error('Failed to send metrics:', error);
    }
  }

  // Send alert to backend
  private async sendAlert(alert: PerformanceAlert): Promise<void> {
    try {
      await supabase
        .from('performance_alerts')
        .insert({
          alert_id: alert.id,
          type: alert.type,
          severity: alert.severity,
          message: alert.message,
          metric: alert.metric,
          value: alert.value,
          threshold: alert.threshold,
          url: alert.url,
          user_id: null, // Would get from auth context
          session_id: this.getSessionId()
        });
    } catch (error) {
      console.error('Failed to send alert:', error);
    }
  }

  // Calculate performance score (0-100)
  private calculatePerformanceScore(metrics: PerformanceMetrics): number {
    const scores = {
      lcp: this.scoreMetric(metrics.lcp, 2500, 4000),
      fid: this.scoreMetric(metrics.fid, 100, 300),
      cls: this.scoreMetric(metrics.cls, 0.1, 0.25),
      fcp: this.scoreMetric(metrics.fcp, 1800, 3000),
      ttfb: this.scoreMetric(metrics.ttfb, 800, 1800)
    };

    // Weighted average (Core Web Vitals have higher weight)
    const weightedScore = (
      scores.lcp * 0.25 +
      scores.fid * 0.25 +
      scores.cls * 0.25 +
      scores.fcp * 0.15 +
      scores.ttfb * 0.1
    );

    return Math.round(weightedScore);
  }

  // Score individual metric (0-100)
  private scoreMetric(value: number, good: number, poor: number): number {
    if (value <= good) return 100;
    if (value >= poor) return 0;
    return Math.round(100 - ((value - good) / (poor - good)) * 100);
  }

  // Get session ID
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('performance_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('performance_session_id', sessionId);
    }
    return sessionId;
  }

  // Throttle utility
  private throttle<T extends (...args: any[]) => any>(func: T, delay: number): T {
    let timeoutId: NodeJS.Timeout | null = null;
    let lastExecTime = 0;
    
    return ((...args: any[]) => {
      const currentTime = Date.now();
      
      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    }) as T;
  }

  // Public API methods
  
  // Get current metrics
  getCurrentMetrics(): PerformanceMetrics | null {
    return this.metrics ? { ...this.metrics } : null;
  }

  // Get performance alerts
  getAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  // Acknowledge alert
  acknowledgeAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
    }
  }

  // Clear acknowledged alerts
  clearAcknowledgedAlerts(): void {
    this.alerts = this.alerts.filter(alert => !alert.acknowledged);
  }

  // Update performance budgets
  updateBudgets(budgets: PerformanceBudget[]): void {
    this.budgets = budgets;
  }

  // Get performance report
  async getPerformanceReport(period: 'day' | 'week' | 'month' = 'day'): Promise<PerformanceReport> {
    try {
      const days = period === 'day' ? 1 : period === 'week' ? 7 : 30;
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

      const { data, error } = await supabase
        .from('web_vitals_metrics')
        .select('*')
        .gte('timestamp', startDate.toISOString());

      if (error) throw error;

      const metrics = data || [];
      
      // Calculate averages
      const averageLCP = metrics.reduce((sum, m) => sum + (m.lcp_value || 0), 0) / metrics.length || 0;
      const averageFID = metrics.reduce((sum, m) => sum + (m.fid_value || 0), 0) / metrics.length || 0;
      const averageCLS = metrics.reduce((sum, m) => sum + (m.cls_value || 0), 0) / metrics.length || 0;
      const performanceScore = metrics.reduce((sum, m) => sum + (m.performance_score || 0), 0) / metrics.length || 0;

      // Generate recommendations
      const recommendations = this.generateRecommendations({
        lcp: averageLCP,
        fid: averageFID,
        cls: averageCLS
      });

      // Check budget status
      const budgetStatus = this.budgets.map(budget => ({
        metric: budget.metric,
        status: this.getBudgetStatus(budget, averageLCP, averageFID, averageCLS),
        value: this.getMetricValue(budget.metric, averageLCP, averageFID, averageCLS),
        budget: budget.budget
      }));

      return {
        period,
        metrics: {
          averageLCP,
          averageFID,
          averageCLS,
          performanceScore
        },
        trends: [], // Would calculate trends from historical data
        recommendations,
        budgetStatus
      };
    } catch (error) {
      console.error('Failed to get performance report:', error);
      throw error;
    }
  }

  // Generate performance recommendations
  private generateRecommendations(metrics: { lcp: number; fid: number; cls: number }): string[] {
    const recommendations: string[] = [];

    if (metrics.lcp > 2500) {
      recommendations.push('Optimize Largest Contentful Paint by reducing server response times and optimizing critical resources');
    }

    if (metrics.fid > 100) {
      recommendations.push('Improve First Input Delay by reducing JavaScript execution time and breaking up long tasks');
    }

    if (metrics.cls > 0.1) {
      recommendations.push('Reduce Cumulative Layout Shift by setting size attributes on images and avoiding dynamic content insertion');
    }

    return recommendations;
  }

  // Get budget status for metric
  private getBudgetStatus(budget: PerformanceBudget, lcp: number, fid: number, cls: number): 'within' | 'warning' | 'exceeded' {
    const value = this.getMetricValue(budget.metric, lcp, fid, cls);
    
    if (value <= budget.budget) return 'within';
    if (value <= budget.warning) return 'warning';
    return 'exceeded';
  }

  // Get metric value by name
  private getMetricValue(metric: string, lcp: number, fid: number, cls: number): number {
    switch (metric) {
      case 'lcp': return lcp;
      case 'fid': return fid;
      case 'cls': return cls;
      default: return 0;
    }
  }

  // Stop monitoring
  stopMonitoring(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.isMonitoring = false;
  }

  // Alert callback
  onAlert?: (alert: PerformanceAlert) => void;
}

// Export singleton instance
export const performanceMonitoringService = new PerformanceMonitoringService();
export default performanceMonitoringService;
