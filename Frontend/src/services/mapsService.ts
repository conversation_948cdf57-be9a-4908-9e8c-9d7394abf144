// =====================================================
// GOOGLE MAPS INTEGRATION SERVICE
// Comprehensive mapping and location services for PHCityRent
// =====================================================

import { supabase } from '@/lib/supabase';

// Types and Interfaces
export interface Location {
  lat: number;
  lng: number;
  address?: string;
  placeId?: string;
}

export interface PropertyLocation extends Location {
  propertyId: string;
  title: string;
  price: number;
  type: string;
  bedrooms?: number;
  bathrooms?: number;
  images?: string[];
}

export interface Amenity {
  id: string;
  name: string;
  type: 'school' | 'hospital' | 'shopping' | 'restaurant' | 'transport' | 'bank' | 'recreation';
  location: Location;
  distance: number;
  rating?: number;
  openNow?: boolean;
}

export interface RouteInfo {
  distance: string;
  duration: string;
  steps: google.maps.DirectionsStep[];
  polyline: string;
}

export interface MapConfig {
  center: Location;
  zoom: number;
  mapTypeId: google.maps.MapTypeId;
  styles?: google.maps.MapTypeStyle[];
}

class GoogleMapsService {
  private apiKey: string;
  private isLoaded: boolean = false;
  private map: google.maps.Map | null = null;
  private geocoder: google.maps.Geocoder | null = null;
  private placesService: google.maps.places.PlacesService | null = null;
  private directionsService: google.maps.DirectionsService | null = null;
  private distanceMatrixService: google.maps.DistanceMatrixService | null = null;

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';
    if (!this.apiKey) {
      console.warn('Google Maps API key not found. Maps functionality will be limited.');
    }
  }

  // Initialize Google Maps API
  async initialize(): Promise<boolean> {
    try {
      if (this.isLoaded) return true;
      
      if (!this.apiKey) {
        throw new Error('Google Maps API key is required');
      }

      // Load Google Maps API
      await this.loadGoogleMapsAPI();
      
      // Initialize services
      this.geocoder = new google.maps.Geocoder();
      this.directionsService = new google.maps.DirectionsService();
      this.distanceMatrixService = new google.maps.DistanceMatrixService();
      
      this.isLoaded = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize Google Maps:', error);
      return false;
    }
  }

  // Load Google Maps API dynamically
  private loadGoogleMapsAPI(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (window.google && window.google.maps) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${this.apiKey}&libraries=places,geometry&callback=initMap`;
      script.async = true;
      script.defer = true;

      (window as any).initMap = () => {
        resolve();
      };

      script.onerror = () => {
        reject(new Error('Failed to load Google Maps API'));
      };

      document.head.appendChild(script);
    });
  }

  // Create interactive property map
  async createPropertyMap(
    container: HTMLElement,
    properties: PropertyLocation[],
    config?: Partial<MapConfig>
  ): Promise<google.maps.Map | null> {
    try {
      await this.initialize();

      const defaultConfig: MapConfig = {
        center: { lat: 4.8156, lng: 7.0498 }, // Port Harcourt center
        zoom: 12,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        styles: this.getCustomMapStyles()
      };

      const mapConfig = { ...defaultConfig, ...config };

      this.map = new google.maps.Map(container, mapConfig);

      // Initialize places service
      this.placesService = new google.maps.places.PlacesService(this.map);

      // Add property markers
      await this.addPropertyMarkers(properties);

      // Fit map to show all properties
      if (properties.length > 0) {
        this.fitMapToProperties(properties);
      }

      return this.map;
    } catch (error) {
      console.error('Failed to create property map:', error);
      return null;
    }
  }

  // Add property markers to map
  private async addPropertyMarkers(properties: PropertyLocation[]): Promise<void> {
    if (!this.map) return;

    const infoWindow = new google.maps.InfoWindow();

    for (const property of properties) {
      const marker = new google.maps.Marker({
        position: { lat: property.lat, lng: property.lng },
        map: this.map,
        title: property.title,
        icon: this.getPropertyMarkerIcon(property.type),
        animation: google.maps.Animation.DROP
      });

      // Add click listener for info window
      marker.addListener('click', () => {
        const content = this.createPropertyInfoWindowContent(property);
        infoWindow.setContent(content);
        infoWindow.open(this.map, marker);
      });
    }
  }

  // Create property info window content
  private createPropertyInfoWindowContent(property: PropertyLocation): string {
    const imageUrl = property.images?.[0] || '/placeholder-property.jpg';
    
    return `
      <div class="property-info-window" style="max-width: 300px;">
        <img src="${imageUrl}" alt="${property.title}" style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px; margin-bottom: 8px;">
        <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">${property.title}</h3>
        <p style="margin: 0 0 4px 0; color: #059669; font-weight: 600; font-size: 18px;">₦${property.price.toLocaleString()}</p>
        <p style="margin: 0 0 8px 0; color: #6b7280; font-size: 14px;">${property.bedrooms || 0} bed • ${property.bathrooms || 0} bath</p>
        <p style="margin: 0 0 8px 0; color: #6b7280; font-size: 12px;">${property.address || 'Address not available'}</p>
        <button onclick="window.open('/properties/${property.propertyId}', '_blank')" 
                style="background: linear-gradient(to right, #f97316, #dc2626); color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;">
          View Details
        </button>
      </div>
    `;
  }

  // Get custom map styles
  private getCustomMapStyles(): google.maps.MapTypeStyle[] {
    return [
      {
        featureType: 'water',
        elementType: 'geometry',
        stylers: [{ color: '#e9e9e9' }, { lightness: 17 }]
      },
      {
        featureType: 'landscape',
        elementType: 'geometry',
        stylers: [{ color: '#f5f5f5' }, { lightness: 20 }]
      },
      {
        featureType: 'road.highway',
        elementType: 'geometry.fill',
        stylers: [{ color: '#ffffff' }, { lightness: 17 }]
      },
      {
        featureType: 'road.highway',
        elementType: 'geometry.stroke',
        stylers: [{ color: '#ffffff' }, { lightness: 29 }, { weight: 0.2 }]
      },
      {
        featureType: 'road.arterial',
        elementType: 'geometry',
        stylers: [{ color: '#ffffff' }, { lightness: 18 }]
      },
      {
        featureType: 'road.local',
        elementType: 'geometry',
        stylers: [{ color: '#ffffff' }, { lightness: 16 }]
      },
      {
        featureType: 'poi',
        elementType: 'geometry',
        stylers: [{ color: '#f5f5f5' }, { lightness: 21 }]
      }
    ];
  }

  // Get property marker icon based on type
  private getPropertyMarkerIcon(propertyType: string): google.maps.Icon {
    const iconColors = {
      apartment: '#3b82f6',
      house: '#059669',
      duplex: '#7c3aed',
      bungalow: '#dc2626',
      default: '#f97316'
    };

    const color = iconColors[propertyType as keyof typeof iconColors] || iconColors.default;

    return {
      url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
        <svg width="32" height="40" viewBox="0 0 32 40" xmlns="http://www.w3.org/2000/svg">
          <path d="M16 0C7.163 0 0 7.163 0 16c0 16 16 24 16 24s16-8 16-24C32 7.163 24.837 0 16 0z" fill="${color}"/>
          <circle cx="16" cy="16" r="8" fill="white"/>
          <text x="16" y="20" text-anchor="middle" fill="${color}" font-family="Arial" font-size="10" font-weight="bold">₦</text>
        </svg>
      `)}`,
      scaledSize: new google.maps.Size(32, 40),
      anchor: new google.maps.Point(16, 40)
    };
  }

  // Fit map to show all properties
  private fitMapToProperties(properties: PropertyLocation[]): void {
    if (!this.map || properties.length === 0) return;

    const bounds = new google.maps.LatLngBounds();
    
    properties.forEach(property => {
      bounds.extend(new google.maps.LatLng(property.lat, property.lng));
    });

    this.map.fitBounds(bounds);

    // Ensure minimum zoom level
    const listener = google.maps.event.addListener(this.map, 'idle', () => {
      if (this.map!.getZoom()! > 15) {
        this.map!.setZoom(15);
      }
      google.maps.event.removeListener(listener);
    });
  }

  // Geocode address to coordinates
  async geocodeAddress(address: string): Promise<Location | null> {
    try {
      await this.initialize();
      
      if (!this.geocoder) {
        throw new Error('Geocoder not initialized');
      }

      return new Promise((resolve, reject) => {
        this.geocoder!.geocode({ address }, (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
            const location = results[0].geometry.location;
            resolve({
              lat: location.lat(),
              lng: location.lng(),
              address: results[0].formatted_address,
              placeId: results[0].place_id
            });
          } else {
            reject(new Error(`Geocoding failed: ${status}`));
          }
        });
      });
    } catch (error) {
      console.error('Geocoding error:', error);
      return null;
    }
  }

  // Reverse geocode coordinates to address
  async reverseGeocode(lat: number, lng: number): Promise<string | null> {
    try {
      await this.initialize();
      
      if (!this.geocoder) {
        throw new Error('Geocoder not initialized');
      }

      return new Promise((resolve, reject) => {
        this.geocoder!.geocode({ location: { lat, lng } }, (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
            resolve(results[0].formatted_address);
          } else {
            reject(new Error(`Reverse geocoding failed: ${status}`));
          }
        });
      });
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return null;
    }
  }

  // Calculate distance between two points
  calculateDistance(point1: Location, point2: Location): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(point2.lat - point1.lat);
    const dLng = this.toRadians(point2.lng - point1.lng);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in kilometers
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  // Find nearby amenities
  async findNearbyAmenities(
    location: Location,
    types: string[] = ['school', 'hospital', 'shopping_mall', 'restaurant', 'bank'],
    radius: number = 5000
  ): Promise<Amenity[]> {
    try {
      await this.initialize();
      
      if (!this.placesService) {
        throw new Error('Places service not initialized');
      }

      const amenities: Amenity[] = [];

      for (const type of types) {
        const places = await this.searchNearbyPlaces(location, type, radius);
        amenities.push(...places);
      }

      return amenities.sort((a, b) => a.distance - b.distance);
    } catch (error) {
      console.error('Error finding nearby amenities:', error);
      return [];
    }
  }

  // Search nearby places
  private searchNearbyPlaces(location: Location, type: string, radius: number): Promise<Amenity[]> {
    return new Promise((resolve, reject) => {
      if (!this.placesService) {
        reject(new Error('Places service not initialized'));
        return;
      }

      const request: google.maps.places.PlaceSearchRequest = {
        location: new google.maps.LatLng(location.lat, location.lng),
        radius,
        type: type as any
      };

      this.placesService.nearbySearch(request, (results, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && results) {
          const amenities: Amenity[] = results.slice(0, 10).map(place => {
            const placeLocation = {
              lat: place.geometry!.location!.lat(),
              lng: place.geometry!.location!.lng()
            };

            return {
              id: place.place_id!,
              name: place.name!,
              type: this.mapPlaceTypeToAmenityType(type),
              location: placeLocation,
              distance: this.calculateDistance(location, placeLocation),
              rating: place.rating,
              openNow: place.opening_hours?.open_now
            };
          });

          resolve(amenities);
        } else {
          resolve([]);
        }
      });
    });
  }

  // Map Google Places types to our amenity types
  private mapPlaceTypeToAmenityType(placeType: string): Amenity['type'] {
    const typeMap: Record<string, Amenity['type']> = {
      school: 'school',
      hospital: 'hospital',
      shopping_mall: 'shopping',
      restaurant: 'restaurant',
      bank: 'bank',
      park: 'recreation',
      gym: 'recreation',
      bus_station: 'transport',
      subway_station: 'transport'
    };

    return typeMap[placeType] || 'recreation';
  }

  // Get route between two points
  async getRoute(origin: Location, destination: Location, travelMode: google.maps.TravelMode = google.maps.TravelMode.DRIVING): Promise<RouteInfo | null> {
    try {
      await this.initialize();
      
      if (!this.directionsService) {
        throw new Error('Directions service not initialized');
      }

      return new Promise((resolve, reject) => {
        this.directionsService!.route({
          origin: new google.maps.LatLng(origin.lat, origin.lng),
          destination: new google.maps.LatLng(destination.lat, destination.lng),
          travelMode
        }, (result, status) => {
          if (status === google.maps.DirectionsStatus.OK && result) {
            const route = result.routes[0];
            const leg = route.legs[0];

            resolve({
              distance: leg.distance!.text,
              duration: leg.duration!.text,
              steps: leg.steps,
              polyline: route.overview_polyline
            });
          } else {
            reject(new Error(`Route calculation failed: ${status}`));
          }
        });
      });
    } catch (error) {
      console.error('Route calculation error:', error);
      return null;
    }
  }

  // Get distance matrix for multiple destinations
  async getDistanceMatrix(
    origins: Location[],
    destinations: Location[],
    travelMode: google.maps.TravelMode = google.maps.TravelMode.DRIVING
  ): Promise<google.maps.DistanceMatrixResponse | null> {
    try {
      await this.initialize();
      
      if (!this.distanceMatrixService) {
        throw new Error('Distance Matrix service not initialized');
      }

      return new Promise((resolve, reject) => {
        this.distanceMatrixService!.getDistanceMatrix({
          origins: origins.map(loc => new google.maps.LatLng(loc.lat, loc.lng)),
          destinations: destinations.map(loc => new google.maps.LatLng(loc.lat, loc.lng)),
          travelMode,
          unitSystem: google.maps.UnitSystem.METRIC
        }, (response, status) => {
          if (status === google.maps.DistanceMatrixStatus.OK && response) {
            resolve(response);
          } else {
            reject(new Error(`Distance matrix calculation failed: ${status}`));
          }
        });
      });
    } catch (error) {
      console.error('Distance matrix error:', error);
      return null;
    }
  }

  // Store location data in Supabase
  async storeLocationData(propertyId: string, location: Location, amenities: Amenity[]): Promise<boolean> {
    try {
      // Store property location
      const { error: locationError } = await supabase
        .from('property_locations')
        .upsert({
          property_id: propertyId,
          latitude: location.lat,
          longitude: location.lng,
          address: location.address,
          place_id: location.placeId,
          updated_at: new Date().toISOString()
        });

      if (locationError) throw locationError;

      // Store amenities
      if (amenities.length > 0) {
        const amenityData = amenities.map(amenity => ({
          property_id: propertyId,
          amenity_id: amenity.id,
          name: amenity.name,
          type: amenity.type,
          latitude: amenity.location.lat,
          longitude: amenity.location.lng,
          distance: amenity.distance,
          rating: amenity.rating,
          open_now: amenity.openNow
        }));

        const { error: amenityError } = await supabase
          .from('property_amenities')
          .upsert(amenityData);

        if (amenityError) throw amenityError;
      }

      return true;
    } catch (error) {
      console.error('Error storing location data:', error);
      return false;
    }
  }

  // Get stored location data
  async getStoredLocationData(propertyId: string): Promise<{ location: Location | null; amenities: Amenity[] }> {
    try {
      // Get property location
      const { data: locationData, error: locationError } = await supabase
        .from('property_locations')
        .select('*')
        .eq('property_id', propertyId)
        .single();

      if (locationError && locationError.code !== 'PGRST116') {
        throw locationError;
      }

      // Get amenities
      const { data: amenityData, error: amenityError } = await supabase
        .from('property_amenities')
        .select('*')
        .eq('property_id', propertyId);

      if (amenityError) throw amenityError;

      const location = locationData ? {
        lat: locationData.latitude,
        lng: locationData.longitude,
        address: locationData.address,
        placeId: locationData.place_id
      } : null;

      const amenities: Amenity[] = amenityData?.map(amenity => ({
        id: amenity.amenity_id,
        name: amenity.name,
        type: amenity.type,
        location: {
          lat: amenity.latitude,
          lng: amenity.longitude
        },
        distance: amenity.distance,
        rating: amenity.rating,
        openNow: amenity.open_now
      })) || [];

      return { location, amenities };
    } catch (error) {
      console.error('Error getting stored location data:', error);
      return { location: null, amenities: [] };
    }
  }
}

// Export singleton instance
export const googleMapsService = new GoogleMapsService();
export default googleMapsService;
