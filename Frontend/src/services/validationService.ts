// =====================================================
// INPUT VALIDATION & SANITIZATION SERVICE
// Comprehensive security validation and XSS prevention
// =====================================================

import DOMPurify from 'dompurify';
import { z } from 'zod';
import { supabase } from '@/lib/supabase';

// Types and Interfaces
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedData?: any;
  securityFlags: SecurityFlag[];
}

export interface SecurityFlag {
  type: 'xss' | 'sql_injection' | 'path_traversal' | 'malicious_file' | 'suspicious_pattern';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  field?: string;
  value?: string;
}

export interface FileValidationConfig {
  allowedTypes: string[];
  maxSize: number;
  allowedExtensions: string[];
  scanForMalware: boolean;
  checkMagicBytes: boolean;
}

export interface ValidationConfig {
  enableXSSProtection: boolean;
  enableSQLInjectionProtection: boolean;
  enablePathTraversalProtection: boolean;
  maxStringLength: number;
  allowedHtmlTags: string[];
  logSecurityEvents: boolean;
}

class ValidationService {
  private config: ValidationConfig = {
    enableXSSProtection: true,
    enableSQLInjectionProtection: true,
    enablePathTraversalProtection: true,
    maxStringLength: 10000,
    allowedHtmlTags: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],
    logSecurityEvents: true
  };

  private xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
    /expression\s*\(/gi,
    /vbscript:/gi,
    /data:text\/html/gi
  ];

  private sqlInjectionPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
    /('|(\\')|(;)|(--)|(\s)|(\/\*)|(\*\/))/gi,
    /(\b(WAITFOR|DELAY)\b)/gi,
    /(\b(CAST|CONVERT|SUBSTRING|ASCII|CHAR)\b)/gi
  ];

  private pathTraversalPatterns = [
    /\.\.\//g,
    /\.\.\\/g,
    /%2e%2e%2f/gi,
    /%2e%2e%5c/gi,
    /\.\.%2f/gi,
    /\.\.%5c/gi
  ];

  // Property validation schemas
  private propertySchema = z.object({
    title: z.string()
      .min(5, 'Title must be at least 5 characters')
      .max(100, 'Title must not exceed 100 characters')
      .regex(/^[a-zA-Z0-9\s\-.,()]+$/, 'Title contains invalid characters'),
    
    description: z.string()
      .min(20, 'Description must be at least 20 characters')
      .max(2000, 'Description must not exceed 2000 characters'),
    
    price: z.number()
      .min(1000, 'Price must be at least ₦1,000')
      .max(50000000, 'Price must not exceed ₦50,000,000'),
    
    location: z.string()
      .min(5, 'Location must be at least 5 characters')
      .max(100, 'Location must not exceed 100 characters')
      .regex(/^[a-zA-Z0-9\s\-.,()]+$/, 'Location contains invalid characters'),
    
    property_type: z.enum(['apartment', 'house', 'duplex', 'bungalow', 'commercial']),
    
    bedrooms: z.number().min(0).max(20),
    bathrooms: z.number().min(0).max(20),
    
    contact_phone: z.string()
      .regex(/^(\+234|0)[789]\d{9}$/, 'Invalid Nigerian phone number format'),
    
    contact_email: z.string()
      .email('Invalid email format')
      .max(100, 'Email must not exceed 100 characters')
  });

  // User validation schemas
  private userSchema = z.object({
    email: z.string()
      .email('Invalid email format')
      .max(100, 'Email must not exceed 100 characters'),
    
    full_name: z.string()
      .min(2, 'Name must be at least 2 characters')
      .max(50, 'Name must not exceed 50 characters')
      .regex(/^[a-zA-Z\s\-']+$/, 'Name contains invalid characters'),
    
    phone: z.string()
      .regex(/^(\+234|0)[789]\d{9}$/, 'Invalid Nigerian phone number format'),
    
    bio: z.string()
      .max(500, 'Bio must not exceed 500 characters')
      .optional(),
    
    location: z.string()
      .max(100, 'Location must not exceed 100 characters')
      .optional()
  });

  // Message validation schema
  private messageSchema = z.object({
    content: z.string()
      .min(1, 'Message cannot be empty')
      .max(1000, 'Message must not exceed 1000 characters'),
    
    recipient_id: z.string().uuid('Invalid recipient ID'),
    conversation_id: z.string().uuid('Invalid conversation ID').optional()
  });

  // Validate property data
  async validateProperty(data: any): Promise<ValidationResult> {
    try {
      const securityFlags: SecurityFlag[] = [];
      
      // Security checks
      await this.performSecurityChecks(data, securityFlags);
      
      // Schema validation
      const validationResult = this.propertySchema.safeParse(data);
      
      if (!validationResult.success) {
        return {
          isValid: false,
          errors: validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
          securityFlags
        };
      }

      // Sanitize data
      const sanitizedData = await this.sanitizeData(validationResult.data);

      // Log security events if any flags were raised
      if (securityFlags.length > 0 && this.config.logSecurityEvents) {
        await this.logSecurityEvent('property_validation', securityFlags, data);
      }

      return {
        isValid: true,
        errors: [],
        sanitizedData,
        securityFlags
      };
    } catch (error) {
      console.error('Property validation error:', error);
      return {
        isValid: false,
        errors: ['Validation failed due to internal error'],
        securityFlags: []
      };
    }
  }

  // Validate user data
  async validateUser(data: any): Promise<ValidationResult> {
    try {
      const securityFlags: SecurityFlag[] = [];
      
      // Security checks
      await this.performSecurityChecks(data, securityFlags);
      
      // Schema validation
      const validationResult = this.userSchema.safeParse(data);
      
      if (!validationResult.success) {
        return {
          isValid: false,
          errors: validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
          securityFlags
        };
      }

      // Sanitize data
      const sanitizedData = await this.sanitizeData(validationResult.data);

      // Log security events
      if (securityFlags.length > 0 && this.config.logSecurityEvents) {
        await this.logSecurityEvent('user_validation', securityFlags, data);
      }

      return {
        isValid: true,
        errors: [],
        sanitizedData,
        securityFlags
      };
    } catch (error) {
      console.error('User validation error:', error);
      return {
        isValid: false,
        errors: ['Validation failed due to internal error'],
        securityFlags: []
      };
    }
  }

  // Validate message data
  async validateMessage(data: any): Promise<ValidationResult> {
    try {
      const securityFlags: SecurityFlag[] = [];
      
      // Security checks
      await this.performSecurityChecks(data, securityFlags);
      
      // Schema validation
      const validationResult = this.messageSchema.safeParse(data);
      
      if (!validationResult.success) {
        return {
          isValid: false,
          errors: validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
          securityFlags
        };
      }

      // Sanitize data
      const sanitizedData = await this.sanitizeData(validationResult.data);

      // Log security events
      if (securityFlags.length > 0 && this.config.logSecurityEvents) {
        await this.logSecurityEvent('message_validation', securityFlags, data);
      }

      return {
        isValid: true,
        errors: [],
        sanitizedData,
        securityFlags
      };
    } catch (error) {
      console.error('Message validation error:', error);
      return {
        isValid: false,
        errors: ['Validation failed due to internal error'],
        securityFlags: []
      };
    }
  }

  // Validate file uploads
  async validateFile(file: File, config: FileValidationConfig): Promise<ValidationResult> {
    try {
      const securityFlags: SecurityFlag[] = [];
      const errors: string[] = [];

      // Check file size
      if (file.size > config.maxSize) {
        errors.push(`File size exceeds maximum allowed size of ${config.maxSize} bytes`);
      }

      // Check file type
      if (!config.allowedTypes.includes(file.type)) {
        errors.push(`File type ${file.type} is not allowed`);
        securityFlags.push({
          type: 'malicious_file',
          severity: 'medium',
          description: `Disallowed file type: ${file.type}`,
          field: 'file_type',
          value: file.type
        });
      }

      // Check file extension
      const extension = file.name.split('.').pop()?.toLowerCase();
      if (extension && !config.allowedExtensions.includes(extension)) {
        errors.push(`File extension .${extension} is not allowed`);
        securityFlags.push({
          type: 'malicious_file',
          severity: 'medium',
          description: `Disallowed file extension: .${extension}`,
          field: 'file_extension',
          value: extension
        });
      }

      // Check magic bytes if enabled
      if (config.checkMagicBytes) {
        const magicBytesValid = await this.validateMagicBytes(file);
        if (!magicBytesValid) {
          errors.push('File content does not match its declared type');
          securityFlags.push({
            type: 'malicious_file',
            severity: 'high',
            description: 'File magic bytes do not match declared type',
            field: 'file_content'
          });
        }
      }

      // Scan for malware if enabled
      if (config.scanForMalware) {
        const malwareDetected = await this.scanForMalware(file);
        if (malwareDetected) {
          errors.push('Malware detected in file');
          securityFlags.push({
            type: 'malicious_file',
            severity: 'critical',
            description: 'Malware detected in uploaded file',
            field: 'file_content'
          });
        }
      }

      // Log security events
      if (securityFlags.length > 0 && this.config.logSecurityEvents) {
        await this.logSecurityEvent('file_validation', securityFlags, {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type
        });
      }

      return {
        isValid: errors.length === 0,
        errors,
        securityFlags
      };
    } catch (error) {
      console.error('File validation error:', error);
      return {
        isValid: false,
        errors: ['File validation failed due to internal error'],
        securityFlags: []
      };
    }
  }

  // Perform comprehensive security checks
  private async performSecurityChecks(data: any, securityFlags: SecurityFlag[]): Promise<void> {
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        // XSS detection
        if (this.config.enableXSSProtection) {
          this.detectXSS(key, value, securityFlags);
        }

        // SQL injection detection
        if (this.config.enableSQLInjectionProtection) {
          this.detectSQLInjection(key, value, securityFlags);
        }

        // Path traversal detection
        if (this.config.enablePathTraversalProtection) {
          this.detectPathTraversal(key, value, securityFlags);
        }

        // Length validation
        if (value.length > this.config.maxStringLength) {
          securityFlags.push({
            type: 'suspicious_pattern',
            severity: 'medium',
            description: `String length exceeds maximum allowed length`,
            field: key,
            value: value.substring(0, 100) + '...'
          });
        }
      }
    }
  }

  // Detect XSS patterns
  private detectXSS(field: string, value: string, securityFlags: SecurityFlag[]): void {
    for (const pattern of this.xssPatterns) {
      if (pattern.test(value)) {
        securityFlags.push({
          type: 'xss',
          severity: 'high',
          description: `Potential XSS attack detected`,
          field,
          value: value.substring(0, 100)
        });
        break;
      }
    }
  }

  // Detect SQL injection patterns
  private detectSQLInjection(field: string, value: string, securityFlags: SecurityFlag[]): void {
    for (const pattern of this.sqlInjectionPatterns) {
      if (pattern.test(value)) {
        securityFlags.push({
          type: 'sql_injection',
          severity: 'critical',
          description: `Potential SQL injection attack detected`,
          field,
          value: value.substring(0, 100)
        });
        break;
      }
    }
  }

  // Detect path traversal patterns
  private detectPathTraversal(field: string, value: string, securityFlags: SecurityFlag[]): void {
    for (const pattern of this.pathTraversalPatterns) {
      if (pattern.test(value)) {
        securityFlags.push({
          type: 'path_traversal',
          severity: 'high',
          description: `Potential path traversal attack detected`,
          field,
          value: value.substring(0, 100)
        });
        break;
      }
    }
  }

  // Sanitize data
  private async sanitizeData(data: any): Promise<any> {
    const sanitized = { ...data };

    for (const [key, value] of Object.entries(sanitized)) {
      if (typeof value === 'string') {
        // HTML sanitization
        sanitized[key] = DOMPurify.sanitize(value, {
          ALLOWED_TAGS: this.config.allowedHtmlTags,
          ALLOWED_ATTR: [],
          KEEP_CONTENT: true
        });

        // Additional sanitization
        sanitized[key] = sanitized[key]
          .replace(/[<>]/g, '') // Remove any remaining angle brackets
          .trim(); // Remove leading/trailing whitespace
      }
    }

    return sanitized;
  }

  // Validate magic bytes
  private async validateMagicBytes(file: File): Promise<boolean> {
    try {
      const buffer = await file.arrayBuffer();
      const bytes = new Uint8Array(buffer.slice(0, 8));
      
      const magicBytes = Array.from(bytes)
        .map(byte => byte.toString(16).padStart(2, '0'))
        .join('');

      // Common file type magic bytes
      const magicBytePatterns: Record<string, string[]> = {
        'image/jpeg': ['ffd8ff'],
        'image/png': ['89504e47'],
        'image/gif': ['474946'],
        'image/webp': ['52494646'],
        'application/pdf': ['255044462d'],
        'text/plain': [], // Text files don't have consistent magic bytes
        'application/json': [], // JSON files don't have magic bytes
      };

      const expectedPatterns = magicBytePatterns[file.type];
      if (!expectedPatterns || expectedPatterns.length === 0) {
        return true; // Skip validation for types without magic bytes
      }

      return expectedPatterns.some(pattern => magicBytes.startsWith(pattern));
    } catch (error) {
      console.error('Magic bytes validation error:', error);
      return false;
    }
  }

  // Scan for malware (simplified implementation)
  private async scanForMalware(file: File): Promise<boolean> {
    try {
      // In production, this would integrate with a real malware scanning service
      // For now, we'll do basic checks for suspicious patterns
      
      const text = await file.text();
      const suspiciousPatterns = [
        /eval\s*\(/gi,
        /exec\s*\(/gi,
        /system\s*\(/gi,
        /shell_exec\s*\(/gi,
        /base64_decode\s*\(/gi,
        /<\?php/gi,
        /<%/gi
      ];

      return suspiciousPatterns.some(pattern => pattern.test(text));
    } catch (error) {
      // If we can't read the file as text, it's likely binary - skip text-based checks
      return false;
    }
  }

  // Log security events
  private async logSecurityEvent(
    eventType: string, 
    securityFlags: SecurityFlag[], 
    data: any
  ): Promise<void> {
    try {
      await supabase
        .from('security_events')
        .insert({
          event_type: eventType,
          severity: this.getHighestSeverity(securityFlags),
          security_flags: securityFlags,
          user_id: null, // Would get from auth context
          ip_address: null, // Would get from request
          user_agent: navigator.userAgent,
          request_data: data,
          timestamp: new Date().toISOString()
        });
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  // Get highest severity from security flags
  private getHighestSeverity(securityFlags: SecurityFlag[]): string {
    const severityOrder = { low: 1, medium: 2, high: 3, critical: 4 };
    
    return securityFlags.reduce((highest, flag) => {
      return severityOrder[flag.severity] > severityOrder[highest] ? flag.severity : highest;
    }, 'low');
  }

  // Sanitize HTML content
  sanitizeHtml(html: string): string {
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: this.config.allowedHtmlTags,
      ALLOWED_ATTR: ['href', 'target'],
      KEEP_CONTENT: true
    });
  }

  // Validate API parameters
  validateApiParams(params: Record<string, any>, schema: z.ZodSchema): ValidationResult {
    try {
      const validationResult = schema.safeParse(params);
      
      if (!validationResult.success) {
        return {
          isValid: false,
          errors: validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
          securityFlags: []
        };
      }

      return {
        isValid: true,
        errors: [],
        sanitizedData: validationResult.data,
        securityFlags: []
      };
    } catch (error) {
      return {
        isValid: false,
        errors: ['Parameter validation failed'],
        securityFlags: []
      };
    }
  }

  // Update configuration
  updateConfig(newConfig: Partial<ValidationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Get current configuration
  getConfig(): ValidationConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const validationService = new ValidationService();
export default validationService;
