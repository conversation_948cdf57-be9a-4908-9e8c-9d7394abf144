import { supabase } from '@/integrations/supabase/client';
import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';

export interface ReportConfig {
  id?: string;
  name: string;
  description: string;
  type: 'agent_performance' | 'property_analysis' | 'market_trends' | 'financial' | 'custom';
  template_id?: string;
  data_sources: string[];
  filters: {
    date_range: { start: string; end: string };
    locations?: string[];
    property_types?: string[];
    agents?: string[];
    custom_filters?: Record<string, any>;
  };
  visualizations: Array<{
    type: 'chart' | 'table' | 'metric' | 'map' | 'text';
    config: any;
    data_query: string;
    position: { x: number; y: number; width: number; height: number };
  }>;
  schedule?: {
    frequency: 'once' | 'daily' | 'weekly' | 'monthly' | 'quarterly';
    time: string;
    recipients: string[];
    format: 'pdf' | 'excel' | 'csv' | 'email';
  };
  sharing: {
    is_public: boolean;
    shared_with: string[];
    access_level: 'view' | 'edit' | 'admin';
  };
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  layout: {
    sections: Array<{
      id: string;
      title: string;
      type: string;
      config: any;
    }>;
  };
  default_filters: any;
  is_system_template: boolean;
  created_by: string;
  created_at: string;
}

export interface GeneratedReport {
  id: string;
  config_id: string;
  name: string;
  format: 'pdf' | 'excel' | 'csv' | 'html';
  file_url: string;
  file_size: number;
  generation_time: number;
  data_points: number;
  generated_at: string;
  generated_by: string;
  download_count: number;
  expires_at?: string;
}

export interface ReportData {
  metadata: {
    report_name: string;
    generated_at: string;
    period: { start: string; end: string };
    filters_applied: any;
    total_records: number;
  };
  sections: Array<{
    title: string;
    type: string;
    data: any;
    visualizations?: any[];
  }>;
  summary: {
    key_metrics: Record<string, number>;
    insights: string[];
    recommendations: string[];
  };
}

export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv' | 'json';
  include_charts: boolean;
  include_raw_data: boolean;
  compression: boolean;
  password_protection?: string;
  watermark?: string;
  custom_styling?: {
    colors: string[];
    fonts: string[];
    logo_url?: string;
  };
}

/**
 * Custom Report Generation Service
 * Provides dynamic report building, scheduling, and export capabilities
 */
export class ReportGenerationService {
  private static instance: ReportGenerationService;
  private reportCache: Map<string, any> = new Map();
  private generationQueue: Map<string, Promise<any>> = new Map();
  private cacheExpiry: number = 30 * 60 * 1000; // 30 minutes

  public static getInstance(): ReportGenerationService {
    if (!ReportGenerationService.instance) {
      ReportGenerationService.instance = new ReportGenerationService();
    }
    return ReportGenerationService.instance;
  }

  // =====================================================
  // REPORT CONFIGURATION MANAGEMENT
  // =====================================================

  /**
   * Create new report configuration
   */
  async createReportConfig(config: Omit<ReportConfig, 'id' | 'created_at' | 'updated_at'>): Promise<ReportConfig> {
    try {
      const { data, error } = await supabase
        .from('report_configs')
        .insert({
          ...config,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error creating report config:', error);
      throw new Error('Failed to create report configuration');
    }
  }

  /**
   * Update report configuration
   */
  async updateReportConfig(configId: string, updates: Partial<ReportConfig>): Promise<ReportConfig> {
    try {
      const { data, error } = await supabase
        .from('report_configs')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', configId)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error updating report config:', error);
      throw new Error('Failed to update report configuration');
    }
  }

  /**
   * Get user's report configurations
   */
  async getUserReportConfigs(userId: string): Promise<ReportConfig[]> {
    try {
      const { data, error } = await supabase
        .from('report_configs')
        .select('*')
        .eq('created_by', userId)
        .order('updated_at', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting user report configs:', error);
      return [];
    }
  }

  // =====================================================
  // REPORT GENERATION
  // =====================================================

  /**
   * Generate report from configuration
   */
  async generateReport(
    configId: string,
    exportOptions: ExportOptions = {
      format: 'pdf',
      include_charts: true,
      include_raw_data: false,
      compression: true
    }
  ): Promise<GeneratedReport> {
    try {
      const cacheKey = `report-${configId}-${JSON.stringify(exportOptions)}`;
      
      // Check if generation is already in progress
      if (this.generationQueue.has(cacheKey)) {
        return await this.generationQueue.get(cacheKey)!;
      }

      // Start generation
      const generationPromise = this.performReportGeneration(configId, exportOptions);
      this.generationQueue.set(cacheKey, generationPromise);

      try {
        const report = await generationPromise;
        return report;
      } finally {
        this.generationQueue.delete(cacheKey);
      }
    } catch (error) {
      console.error('Error generating report:', error);
      throw new Error('Failed to generate report');
    }
  }

  /**
   * Perform report generation
   */
  private async performReportGeneration(
    configId: string,
    exportOptions: ExportOptions
  ): Promise<GeneratedReport> {
    const startTime = Date.now();

    // Get report configuration
    const { data: config, error: configError } = await supabase
      .from('report_configs')
      .select('*')
      .eq('id', configId)
      .single();

    if (configError) throw configError;

    // Collect report data
    const reportData = await this.collectReportData(config);

    // Generate file based on format
    let fileUrl: string;
    let fileSize: number;

    switch (exportOptions.format) {
      case 'pdf':
        ({ fileUrl, fileSize } = await this.generatePDFReport(reportData, exportOptions));
        break;
      case 'excel':
        ({ fileUrl, fileSize } = await this.generateExcelReport(reportData, exportOptions));
        break;
      case 'csv':
        ({ fileUrl, fileSize } = await this.generateCSVReport(reportData, exportOptions));
        break;
      default:
        throw new Error(`Unsupported export format: ${exportOptions.format}`);
    }

    const generationTime = Date.now() - startTime;

    // Save generated report record
    const { data: reportRecord, error: saveError } = await supabase
      .from('generated_reports')
      .insert({
        config_id: configId,
        name: config.name,
        format: exportOptions.format,
        file_url: fileUrl,
        file_size: fileSize,
        generation_time: generationTime,
        data_points: reportData.metadata.total_records,
        generated_at: new Date().toISOString(),
        generated_by: config.created_by,
        download_count: 0
      })
      .select()
      .single();

    if (saveError) throw saveError;

    return reportRecord;
  }

  /**
   * Collect data for report generation
   */
  private async collectReportData(config: ReportConfig): Promise<ReportData> {
    try {
      const sections: ReportData['sections'] = [];
      let totalRecords = 0;

      // Process each visualization/section
      for (const viz of config.visualizations) {
        const { data: sectionData, error } = await supabase.rpc('execute_report_query', {
          query: viz.data_query,
          filters: config.filters
        });

        if (error) {
          console.error('Error executing report query:', error);
          continue;
        }

        sections.push({
          title: viz.config.title || 'Untitled Section',
          type: viz.type,
          data: sectionData,
          visualizations: viz.type === 'chart' ? [viz.config] : undefined
        });

        totalRecords += Array.isArray(sectionData) ? sectionData.length : 1;
      }

      // Generate summary insights
      const summary = await this.generateReportSummary(sections, config);

      return {
        metadata: {
          report_name: config.name,
          generated_at: new Date().toISOString(),
          period: config.filters.date_range,
          filters_applied: config.filters,
          total_records: totalRecords
        },
        sections,
        summary
      };
    } catch (error) {
      console.error('Error collecting report data:', error);
      throw new Error('Failed to collect report data');
    }
  }

  // =====================================================
  // EXPORT FUNCTIONS
  // =====================================================

  /**
   * Generate PDF report
   */
  private async generatePDFReport(
    reportData: ReportData,
    options: ExportOptions
  ): Promise<{ fileUrl: string; fileSize: number }> {
    try {
      const pdf = new jsPDF();
      let yPosition = 20;

      // Add header
      pdf.setFontSize(20);
      pdf.text(reportData.metadata.report_name, 20, yPosition);
      yPosition += 15;

      pdf.setFontSize(12);
      pdf.text(`Generated: ${new Date(reportData.metadata.generated_at).toLocaleString()}`, 20, yPosition);
      yPosition += 10;
      pdf.text(`Period: ${reportData.metadata.period.start} to ${reportData.metadata.period.end}`, 20, yPosition);
      yPosition += 20;

      // Add sections
      for (const section of reportData.sections) {
        // Check if we need a new page
        if (yPosition > 250) {
          pdf.addPage();
          yPosition = 20;
        }

        // Section title
        pdf.setFontSize(16);
        pdf.text(section.title, 20, yPosition);
        yPosition += 15;

        // Section content
        pdf.setFontSize(10);
        if (section.type === 'table' && Array.isArray(section.data)) {
          // Add table data
          const tableData = section.data.slice(0, 20); // Limit rows for PDF
          for (const row of tableData) {
            const rowText = Object.values(row).join(' | ');
            pdf.text(rowText.substring(0, 80), 20, yPosition);
            yPosition += 8;
            
            if (yPosition > 270) {
              pdf.addPage();
              yPosition = 20;
            }
          }
        } else if (section.type === 'metric') {
          pdf.text(`Value: ${section.data}`, 20, yPosition);
          yPosition += 10;
        }

        yPosition += 10;
      }

      // Add summary
      if (yPosition > 200) {
        pdf.addPage();
        yPosition = 20;
      }

      pdf.setFontSize(16);
      pdf.text('Summary', 20, yPosition);
      yPosition += 15;

      pdf.setFontSize(10);
      for (const insight of reportData.summary.insights) {
        pdf.text(`• ${insight}`, 20, yPosition);
        yPosition += 8;
      }

      // Convert to blob and upload
      const pdfBlob = pdf.output('blob');
      const fileName = `report-${Date.now()}.pdf`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('reports')
        .upload(fileName, pdfBlob);

      if (uploadError) throw uploadError;

      const { data: urlData } = supabase.storage
        .from('reports')
        .getPublicUrl(fileName);

      return {
        fileUrl: urlData.publicUrl,
        fileSize: pdfBlob.size
      };
    } catch (error) {
      console.error('Error generating PDF report:', error);
      throw new Error('Failed to generate PDF report');
    }
  }

  /**
   * Generate Excel report
   */
  private async generateExcelReport(
    reportData: ReportData,
    options: ExportOptions
  ): Promise<{ fileUrl: string; fileSize: number }> {
    try {
      const workbook = XLSX.utils.book_new();

      // Add metadata sheet
      const metadataSheet = XLSX.utils.json_to_sheet([reportData.metadata]);
      XLSX.utils.book_append_sheet(workbook, metadataSheet, 'Metadata');

      // Add data sheets
      for (const section of reportData.sections) {
        if (Array.isArray(section.data)) {
          const worksheet = XLSX.utils.json_to_sheet(section.data);
          const sheetName = section.title.substring(0, 31); // Excel sheet name limit
          XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
        }
      }

      // Add summary sheet
      const summaryData = [
        { Type: 'Key Metrics', ...reportData.summary.key_metrics },
        ...reportData.summary.insights.map((insight, index) => ({
          Type: 'Insight',
          Index: index + 1,
          Description: insight
        }))
      ];
      const summarySheet = XLSX.utils.json_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

      // Convert to buffer
      const excelBuffer = XLSX.write(workbook, { type: 'array', bookType: 'xlsx' });
      const excelBlob = new Blob([excelBuffer], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });

      const fileName = `report-${Date.now()}.xlsx`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('reports')
        .upload(fileName, excelBlob);

      if (uploadError) throw uploadError;

      const { data: urlData } = supabase.storage
        .from('reports')
        .getPublicUrl(fileName);

      return {
        fileUrl: urlData.publicUrl,
        fileSize: excelBlob.size
      };
    } catch (error) {
      console.error('Error generating Excel report:', error);
      throw new Error('Failed to generate Excel report');
    }
  }

  /**
   * Generate CSV report
   */
  private async generateCSVReport(
    reportData: ReportData,
    options: ExportOptions
  ): Promise<{ fileUrl: string; fileSize: number }> {
    try {
      let csvContent = '';

      // Add metadata
      csvContent += `Report Name,${reportData.metadata.report_name}\n`;
      csvContent += `Generated At,${reportData.metadata.generated_at}\n`;
      csvContent += `Period Start,${reportData.metadata.period.start}\n`;
      csvContent += `Period End,${reportData.metadata.period.end}\n\n`;

      // Add sections
      for (const section of reportData.sections) {
        csvContent += `${section.title}\n`;
        
        if (Array.isArray(section.data) && section.data.length > 0) {
          // Add headers
          const headers = Object.keys(section.data[0]);
          csvContent += headers.join(',') + '\n';
          
          // Add data rows
          for (const row of section.data) {
            const values = headers.map(header => {
              const value = row[header];
              return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
            });
            csvContent += values.join(',') + '\n';
          }
        }
        csvContent += '\n';
      }

      const csvBlob = new Blob([csvContent], { type: 'text/csv' });
      const fileName = `report-${Date.now()}.csv`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('reports')
        .upload(fileName, csvBlob);

      if (uploadError) throw uploadError;

      const { data: urlData } = supabase.storage
        .from('reports')
        .getPublicUrl(fileName);

      return {
        fileUrl: urlData.publicUrl,
        fileSize: csvBlob.size
      };
    } catch (error) {
      console.error('Error generating CSV report:', error);
      throw new Error('Failed to generate CSV report');
    }
  }

  /**
   * Generate report summary
   */
  private async generateReportSummary(
    sections: ReportData['sections'],
    config: ReportConfig
  ): Promise<ReportData['summary']> {
    const keyMetrics: Record<string, number> = {};
    const insights: string[] = [];
    const recommendations: string[] = [];

    // Extract key metrics from sections
    for (const section of sections) {
      if (section.type === 'metric') {
        keyMetrics[section.title] = typeof section.data === 'number' ? section.data : 0;
      } else if (Array.isArray(section.data)) {
        keyMetrics[`${section.title} Count`] = section.data.length;
      }
    }

    // Generate basic insights
    insights.push(`Report contains ${sections.length} sections with ${Object.keys(keyMetrics).length} key metrics`);
    
    if (config.type === 'agent_performance') {
      insights.push('Agent performance analysis completed');
    } else if (config.type === 'market_trends') {
      insights.push('Market trend analysis shows current market conditions');
    }

    // Generate basic recommendations
    recommendations.push('Review key metrics regularly for optimal performance');
    recommendations.push('Consider setting up automated alerts for critical metrics');

    return {
      key_metrics: keyMetrics,
      insights,
      recommendations
    };
  }
}

// Export singleton instance
export const reportGenerationService = ReportGenerationService.getInstance();
