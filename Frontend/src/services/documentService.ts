// =====================================================
// DOCUMENT GENERATION SERVICE
// Automated document creation and management for PHCityRent
// =====================================================

import { supabase } from '@/lib/supabase';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// Types and Interfaces
export interface DocumentTemplate {
  id: string;
  name: string;
  type: 'rental_agreement' | 'lease_contract' | 'property_inspection' | 'receipt' | 'invoice' | 'legal_notice';
  category: 'legal' | 'financial' | 'administrative';
  template: string;
  variables: DocumentVariable[];
  isActive: boolean;
  version: string;
  createdAt: string;
  updatedAt: string;
}

export interface DocumentVariable {
  name: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'select' | 'textarea';
  label: string;
  required: boolean;
  defaultValue?: any;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

export interface DocumentData {
  templateId: string;
  variables: Record<string, any>;
  metadata: {
    propertyId?: string;
    tenantId?: string;
    landlordId?: string;
    agentId?: string;
    title: string;
    description?: string;
  };
}

export interface GeneratedDocument {
  id: string;
  templateId: string;
  title: string;
  content: string;
  pdfUrl?: string;
  status: 'draft' | 'generated' | 'signed' | 'executed' | 'archived';
  version: number;
  signatures: DocumentSignature[];
  metadata: any;
  createdAt: string;
  updatedAt: string;
}

export interface DocumentSignature {
  id: string;
  documentId: string;
  signerId: string;
  signerName: string;
  signerEmail: string;
  signerRole: 'tenant' | 'landlord' | 'agent' | 'witness';
  signatureData?: string;
  signedAt?: string;
  ipAddress?: string;
  status: 'pending' | 'signed' | 'declined';
}

export interface ComplianceCheck {
  rule: string;
  description: string;
  status: 'passed' | 'failed' | 'warning';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface DocumentVersion {
  id: string;
  documentId: string;
  version: number;
  content: string;
  changes: string[];
  createdBy: string;
  createdAt: string;
}

class DocumentService {
  private templates: Map<string, DocumentTemplate> = new Map();
  private complianceRules: Map<string, (data: any) => ComplianceCheck[]> = new Map();

  constructor() {
    this.initializeTemplates();
    this.initializeComplianceRules();
  }

  // Initialize document templates
  private initializeTemplates(): void {
    // Load templates from database or initialize default ones
    this.loadTemplatesFromDatabase();
  }

  // Load templates from database
  private async loadTemplatesFromDatabase(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('document_templates')
        .select('*')
        .eq('is_active', true);

      if (error) throw error;

      data?.forEach(template => {
        this.templates.set(template.id, {
          id: template.id,
          name: template.name,
          type: template.type,
          category: template.category,
          template: template.template,
          variables: template.variables,
          isActive: template.is_active,
          version: template.version,
          createdAt: template.created_at,
          updatedAt: template.updated_at
        });
      });
    } catch (error) {
      console.error('Error loading templates:', error);
      this.initializeDefaultTemplates();
    }
  }

  // Initialize default templates
  private initializeDefaultTemplates(): void {
    const defaultTemplates: DocumentTemplate[] = [
      {
        id: 'rental-agreement-ng',
        name: 'Nigerian Rental Agreement',
        type: 'rental_agreement',
        category: 'legal',
        template: this.getRentalAgreementTemplate(),
        variables: this.getRentalAgreementVariables(),
        isActive: true,
        version: '1.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'lease-contract-ng',
        name: 'Nigerian Lease Contract',
        type: 'lease_contract',
        category: 'legal',
        template: this.getLeaseContractTemplate(),
        variables: this.getLeaseContractVariables(),
        isActive: true,
        version: '1.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'property-inspection',
        name: 'Property Inspection Report',
        type: 'property_inspection',
        category: 'administrative',
        template: this.getInspectionTemplate(),
        variables: this.getInspectionVariables(),
        isActive: true,
        version: '1.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  // Generate document from template
  async generateDocument(documentData: DocumentData): Promise<GeneratedDocument | null> {
    try {
      const template = this.templates.get(documentData.templateId);
      if (!template) {
        throw new Error(`Template not found: ${documentData.templateId}`);
      }

      // Validate variables
      const validationErrors = this.validateDocumentData(template, documentData.variables);
      if (validationErrors.length > 0) {
        throw new Error(`Validation errors: ${validationErrors.join(', ')}`);
      }

      // Process template
      const processedContent = this.processTemplate(template.template, documentData.variables);

      // Run compliance checks
      const complianceResults = await this.runComplianceChecks(template.type, documentData);

      // Create document record
      const { data, error } = await supabase
        .from('generated_documents')
        .insert({
          template_id: documentData.templateId,
          title: documentData.metadata.title,
          content: processedContent,
          status: 'generated',
          version: 1,
          metadata: {
            ...documentData.metadata,
            complianceResults,
            variables: documentData.variables
          }
        })
        .select()
        .single();

      if (error) throw error;

      // Generate PDF
      const pdfUrl = await this.generatePDF(data.id, processedContent, documentData.metadata.title);

      // Update document with PDF URL
      if (pdfUrl) {
        await supabase
          .from('generated_documents')
          .update({ pdf_url: pdfUrl })
          .eq('id', data.id);
      }

      return {
        id: data.id,
        templateId: data.template_id,
        title: data.title,
        content: data.content,
        pdfUrl,
        status: data.status,
        version: data.version,
        signatures: [],
        metadata: data.metadata,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
    } catch (error) {
      console.error('Error generating document:', error);
      return null;
    }
  }

  // Process template with variables
  private processTemplate(template: string, variables: Record<string, any>): string {
    let processedTemplate = template;

    // Replace variables in template
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      processedTemplate = processedTemplate.replace(regex, this.formatValue(value));
    });

    // Replace date placeholders
    processedTemplate = processedTemplate.replace(/{{current_date}}/g, new Date().toLocaleDateString('en-NG'));
    processedTemplate = processedTemplate.replace(/{{current_year}}/g, new Date().getFullYear().toString());

    return processedTemplate;
  }

  // Format value for template
  private formatValue(value: any): string {
    if (value === null || value === undefined) return '';
    
    if (typeof value === 'number') {
      return value.toLocaleString('en-NG');
    }
    
    if (value instanceof Date) {
      return value.toLocaleDateString('en-NG');
    }
    
    return String(value);
  }

  // Validate document data
  private validateDocumentData(template: DocumentTemplate, variables: Record<string, any>): string[] {
    const errors: string[] = [];

    template.variables.forEach(variable => {
      const value = variables[variable.name];

      // Check required fields
      if (variable.required && (value === null || value === undefined || value === '')) {
        errors.push(`${variable.label} is required`);
        return;
      }

      // Skip validation if value is empty and not required
      if (!value && !variable.required) return;

      // Type validation
      switch (variable.type) {
        case 'number':
          if (isNaN(Number(value))) {
            errors.push(`${variable.label} must be a number`);
          }
          break;
        case 'date':
          if (!(value instanceof Date) && isNaN(Date.parse(value))) {
            errors.push(`${variable.label} must be a valid date`);
          }
          break;
        case 'select':
          if (variable.options && !variable.options.includes(value)) {
            errors.push(`${variable.label} must be one of: ${variable.options.join(', ')}`);
          }
          break;
      }

      // Custom validation
      if (variable.validation) {
        const validation = variable.validation;
        
        if (validation.min !== undefined && Number(value) < validation.min) {
          errors.push(`${variable.label} must be at least ${validation.min}`);
        }
        
        if (validation.max !== undefined && Number(value) > validation.max) {
          errors.push(`${variable.label} must be at most ${validation.max}`);
        }
        
        if (validation.pattern && !new RegExp(validation.pattern).test(String(value))) {
          errors.push(validation.message || `${variable.label} format is invalid`);
        }
      }
    });

    return errors;
  }

  // Generate PDF from HTML content
  private async generatePDF(documentId: string, htmlContent: string, title: string): Promise<string | null> {
    try {
      // Create a temporary container for the HTML content
      const container = document.createElement('div');
      container.innerHTML = htmlContent;
      container.style.width = '210mm'; // A4 width
      container.style.padding = '20mm';
      container.style.fontFamily = 'Arial, sans-serif';
      container.style.fontSize = '12px';
      container.style.lineHeight = '1.5';
      container.style.color = '#000';
      container.style.backgroundColor = '#fff';
      
      // Temporarily add to DOM
      document.body.appendChild(container);

      // Convert to canvas
      const canvas = await html2canvas(container, {
        scale: 2,
        useCORS: true,
        allowTaint: true
      });

      // Remove from DOM
      document.body.removeChild(container);

      // Create PDF
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgData = canvas.toDataURL('image/png');
      
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = (canvas.height * pdfWidth) / canvas.width;
      
      pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);

      // Convert to blob
      const pdfBlob = pdf.output('blob');

      // Upload to Supabase storage
      const fileName = `documents/${documentId}/${title.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.pdf`;
      
      const { data, error } = await supabase.storage
        .from('documents')
        .upload(fileName, pdfBlob, {
          contentType: 'application/pdf',
          upsert: true
        });

      if (error) throw error;

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('documents')
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Error generating PDF:', error);
      return null;
    }
  }

  // Initialize compliance rules
  private initializeComplianceRules(): void {
    // Nigerian rental agreement compliance
    this.complianceRules.set('rental_agreement', (data: any) => {
      const checks: ComplianceCheck[] = [];

      // Check rent amount
      if (!data.variables.rent_amount || data.variables.rent_amount <= 0) {
        checks.push({
          rule: 'rent_amount_required',
          description: 'Rent amount must be specified',
          status: 'failed',
          message: 'Rent amount is required and must be greater than 0',
          severity: 'critical'
        });
      }

      // Check lease duration
      if (!data.variables.lease_duration || data.variables.lease_duration < 6) {
        checks.push({
          rule: 'minimum_lease_duration',
          description: 'Minimum lease duration compliance',
          status: 'warning',
          message: 'Lease duration should be at least 6 months for legal protection',
          severity: 'medium'
        });
      }

      // Check security deposit
      const rentAmount = data.variables.rent_amount || 0;
      const securityDeposit = data.variables.security_deposit || 0;
      
      if (securityDeposit > rentAmount * 2) {
        checks.push({
          rule: 'security_deposit_limit',
          description: 'Security deposit limit compliance',
          status: 'warning',
          message: 'Security deposit exceeds recommended limit of 2 months rent',
          severity: 'medium'
        });
      }

      // Check property description
      if (!data.variables.property_description || data.variables.property_description.length < 50) {
        checks.push({
          rule: 'property_description_detail',
          description: 'Property description adequacy',
          status: 'warning',
          message: 'Property description should be detailed for legal clarity',
          severity: 'low'
        });
      }

      return checks;
    });

    // Add more compliance rules for other document types
    this.complianceRules.set('lease_contract', (data: any) => {
      // Lease contract specific compliance checks
      return [];
    });
  }

  // Run compliance checks
  private async runComplianceChecks(documentType: string, documentData: DocumentData): Promise<ComplianceCheck[]> {
    const complianceFunction = this.complianceRules.get(documentType);
    if (!complianceFunction) return [];

    return complianceFunction(documentData);
  }

  // Get available templates
  async getTemplates(category?: string): Promise<DocumentTemplate[]> {
    const templates = Array.from(this.templates.values());
    
    if (category) {
      return templates.filter(template => template.category === category);
    }
    
    return templates;
  }

  // Get template by ID
  getTemplate(templateId: string): DocumentTemplate | null {
    return this.templates.get(templateId) || null;
  }

  // Create document signature request
  async createSignatureRequest(documentId: string, signers: Omit<DocumentSignature, 'id' | 'documentId' | 'status'>[]): Promise<boolean> {
    try {
      const signatureData = signers.map(signer => ({
        document_id: documentId,
        signer_id: signer.signerId,
        signer_name: signer.signerName,
        signer_email: signer.signerEmail,
        signer_role: signer.signerRole,
        status: 'pending'
      }));

      const { error } = await supabase
        .from('document_signatures')
        .insert(signatureData);

      if (error) throw error;

      // Send signature request emails
      await this.sendSignatureRequestEmails(documentId, signers);

      return true;
    } catch (error) {
      console.error('Error creating signature request:', error);
      return false;
    }
  }

  // Send signature request emails
  private async sendSignatureRequestEmails(documentId: string, signers: any[]): Promise<void> {
    // Implementation would integrate with email service
    // For now, we'll log the action
    console.log(`Signature request emails sent for document ${documentId} to:`, signers.map(s => s.signerEmail));
  }

  // Get document by ID
  async getDocument(documentId: string): Promise<GeneratedDocument | null> {
    try {
      const { data, error } = await supabase
        .from('generated_documents')
        .select(`
          *,
          signatures:document_signatures(*)
        `)
        .eq('id', documentId)
        .single();

      if (error) throw error;

      return {
        id: data.id,
        templateId: data.template_id,
        title: data.title,
        content: data.content,
        pdfUrl: data.pdf_url,
        status: data.status,
        version: data.version,
        signatures: data.signatures || [],
        metadata: data.metadata,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
    } catch (error) {
      console.error('Error getting document:', error);
      return null;
    }
  }

  // Update document status
  async updateDocumentStatus(documentId: string, status: GeneratedDocument['status']): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('generated_documents')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', documentId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating document status:', error);
      return false;
    }
  }

  // Create document version
  async createDocumentVersion(documentId: string, content: string, changes: string[], createdBy: string): Promise<boolean> {
    try {
      // Get current version
      const { data: currentDoc } = await supabase
        .from('generated_documents')
        .select('version')
        .eq('id', documentId)
        .single();

      const newVersion = (currentDoc?.version || 0) + 1;

      // Create version record
      const { error: versionError } = await supabase
        .from('document_versions')
        .insert({
          document_id: documentId,
          version: newVersion,
          content,
          changes,
          created_by: createdBy
        });

      if (versionError) throw versionError;

      // Update document
      const { error: updateError } = await supabase
        .from('generated_documents')
        .update({
          content,
          version: newVersion,
          updated_at: new Date().toISOString()
        })
        .eq('id', documentId);

      if (updateError) throw updateError;

      return true;
    } catch (error) {
      console.error('Error creating document version:', error);
      return false;
    }
  }

  // Get document versions
  async getDocumentVersions(documentId: string): Promise<DocumentVersion[]> {
    try {
      const { data, error } = await supabase
        .from('document_versions')
        .select('*')
        .eq('document_id', documentId)
        .order('version', { ascending: false });

      if (error) throw error;

      return data?.map(version => ({
        id: version.id,
        documentId: version.document_id,
        version: version.version,
        content: version.content,
        changes: version.changes,
        createdBy: version.created_by,
        createdAt: version.created_at
      })) || [];
    } catch (error) {
      console.error('Error getting document versions:', error);
      return [];
    }
  }

  // Template content methods (would be much longer in real implementation)
  private getRentalAgreementTemplate(): string {
    return `
      <div class="document">
        <h1>RENTAL AGREEMENT</h1>
        <p><strong>Date:</strong> {{current_date}}</p>
        
        <h2>PARTIES</h2>
        <p><strong>Landlord:</strong> {{landlord_name}}</p>
        <p><strong>Address:</strong> {{landlord_address}}</p>
        <p><strong>Phone:</strong> {{landlord_phone}}</p>
        
        <p><strong>Tenant:</strong> {{tenant_name}}</p>
        <p><strong>Address:</strong> {{tenant_address}}</p>
        <p><strong>Phone:</strong> {{tenant_phone}}</p>
        
        <h2>PROPERTY DETAILS</h2>
        <p><strong>Property Address:</strong> {{property_address}}</p>
        <p><strong>Description:</strong> {{property_description}}</p>
        
        <h2>RENTAL TERMS</h2>
        <p><strong>Monthly Rent:</strong> ₦{{rent_amount}}</p>
        <p><strong>Security Deposit:</strong> ₦{{security_deposit}}</p>
        <p><strong>Lease Duration:</strong> {{lease_duration}} months</p>
        <p><strong>Start Date:</strong> {{start_date}}</p>
        <p><strong>End Date:</strong> {{end_date}}</p>
        
        <h2>TERMS AND CONDITIONS</h2>
        <p>{{terms_and_conditions}}</p>
        
        <div class="signatures">
          <div class="signature-block">
            <p>Landlord Signature: _________________</p>
            <p>Date: _________________</p>
          </div>
          <div class="signature-block">
            <p>Tenant Signature: _________________</p>
            <p>Date: _________________</p>
          </div>
        </div>
      </div>
    `;
  }

  private getRentalAgreementVariables(): DocumentVariable[] {
    return [
      { name: 'landlord_name', type: 'text', label: 'Landlord Name', required: true },
      { name: 'landlord_address', type: 'textarea', label: 'Landlord Address', required: true },
      { name: 'landlord_phone', type: 'text', label: 'Landlord Phone', required: true },
      { name: 'tenant_name', type: 'text', label: 'Tenant Name', required: true },
      { name: 'tenant_address', type: 'textarea', label: 'Tenant Address', required: true },
      { name: 'tenant_phone', type: 'text', label: 'Tenant Phone', required: true },
      { name: 'property_address', type: 'textarea', label: 'Property Address', required: true },
      { name: 'property_description', type: 'textarea', label: 'Property Description', required: true },
      { name: 'rent_amount', type: 'number', label: 'Monthly Rent Amount', required: true },
      { name: 'security_deposit', type: 'number', label: 'Security Deposit', required: true },
      { name: 'lease_duration', type: 'number', label: 'Lease Duration (months)', required: true },
      { name: 'start_date', type: 'date', label: 'Lease Start Date', required: true },
      { name: 'end_date', type: 'date', label: 'Lease End Date', required: true },
      { name: 'terms_and_conditions', type: 'textarea', label: 'Additional Terms and Conditions', required: false }
    ];
  }

  private getLeaseContractTemplate(): string {
    return `<div class="document"><h1>LEASE CONTRACT</h1><!-- Lease contract template --></div>`;
  }

  private getLeaseContractVariables(): DocumentVariable[] {
    return [];
  }

  private getInspectionTemplate(): string {
    return `<div class="document"><h1>PROPERTY INSPECTION REPORT</h1><!-- Inspection template --></div>`;
  }

  private getInspectionVariables(): DocumentVariable[] {
    return [];
  }
}

// Export singleton instance
export const documentService = new DocumentService();
export default documentService;
