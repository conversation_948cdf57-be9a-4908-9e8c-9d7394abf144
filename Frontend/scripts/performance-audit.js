#!/usr/bin/env node

/**
 * Performance Audit Script for PHCityRent Frontend
 * Runs comprehensive performance tests and generates reports
 */

const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  urls: [
    'http://localhost:8080',
    'http://localhost:8080/properties',
    'http://localhost:8080/search',
    'http://localhost:8080/agents',
    'http://localhost:8080/auth',
  ],
  outputDir: './performance-reports',
  thresholds: {
    performance: 95,
    accessibility: 90,
    bestPractices: 90,
    seo: 90,
    pwa: 80,
  },
  lighthouse: {
    onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'],
    settings: {
      formFactor: 'desktop',
      throttling: {
        rttMs: 40,
        throughputKbps: 10240,
        cpuSlowdownMultiplier: 1,
        requestLatencyMs: 0,
        downloadThroughputKbps: 0,
        uploadThroughputKbps: 0,
      },
      screenEmulation: {
        mobile: false,
        width: 1350,
        height: 940,
        deviceScaleFactor: 1,
        disabled: false,
      },
      emulatedUserAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36',
    },
  },
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

async function launchChrome() {
  return await chromeLauncher.launch({
    chromeFlags: [
      '--headless',
      '--disable-gpu',
      '--no-sandbox',
      '--disable-dev-shm-usage',
      '--disable-extensions',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
    ],
  });
}

async function runLighthouseAudit(url, chrome) {
  console.log(`🔍 Auditing: ${url}`);
  
  const options = {
    logLevel: 'info',
    output: 'html',
    port: chrome.port,
    ...config.lighthouse,
  };

  const runnerResult = await lighthouse(url, options);
  
  if (!runnerResult) {
    throw new Error(`Failed to run Lighthouse for ${url}`);
  }

  return runnerResult;
}

function generateSummaryReport(results) {
  const summary = {
    timestamp: new Date().toISOString(),
    totalUrls: results.length,
    averageScores: {},
    failedThresholds: [],
    recommendations: [],
  };

  // Calculate average scores
  const categories = ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'];
  categories.forEach(category => {
    const scores = results.map(result => 
      result.lhr.categories[category]?.score * 100 || 0
    );
    summary.averageScores[category] = Math.round(
      scores.reduce((sum, score) => sum + score, 0) / scores.length
    );
  });

  // Check thresholds
  Object.entries(config.thresholds).forEach(([category, threshold]) => {
    const avgScore = summary.averageScores[category];
    if (avgScore < threshold) {
      summary.failedThresholds.push({
        category,
        score: avgScore,
        threshold,
        difference: threshold - avgScore,
      });
    }
  });

  // Generate recommendations
  if (summary.averageScores.performance < 95) {
    summary.recommendations.push({
      category: 'Performance',
      priority: 'High',
      suggestion: 'Optimize bundle size and implement more aggressive code splitting',
    });
  }

  if (summary.averageScores.accessibility < 90) {
    summary.recommendations.push({
      category: 'Accessibility',
      priority: 'High',
      suggestion: 'Add missing alt texts and improve keyboard navigation',
    });
  }

  return summary;
}

function saveReport(result, url, index) {
  const urlSlug = url.replace(/[^a-zA-Z0-9]/g, '_');
  const timestamp = new Date().toISOString().split('T')[0];
  
  // Save HTML report
  const htmlPath = path.join(
    config.outputDir,
    `lighthouse_${urlSlug}_${timestamp}.html`
  );
  fs.writeFileSync(htmlPath, result.report);
  
  // Save JSON data
  const jsonPath = path.join(
    config.outputDir,
    `lighthouse_${urlSlug}_${timestamp}.json`
  );
  fs.writeFileSync(jsonPath, JSON.stringify(result.lhr, null, 2));
  
  console.log(`📊 Report saved: ${htmlPath}`);
  
  return {
    url,
    htmlPath,
    jsonPath,
    lhr: result.lhr,
  };
}

function printResults(results, summary) {
  console.log('\n🎯 PERFORMANCE AUDIT RESULTS');
  console.log('================================');
  
  // Print individual URL scores
  results.forEach(result => {
    console.log(`\n📍 ${result.url}`);
    console.log(`   Performance: ${Math.round(result.lhr.categories.performance.score * 100)}`);
    console.log(`   Accessibility: ${Math.round(result.lhr.categories.accessibility.score * 100)}`);
    console.log(`   Best Practices: ${Math.round(result.lhr.categories['best-practices'].score * 100)}`);
    console.log(`   SEO: ${Math.round(result.lhr.categories.seo.score * 100)}`);
    console.log(`   PWA: ${Math.round((result.lhr.categories.pwa?.score || 0) * 100)}`);
  });

  // Print summary
  console.log('\n📈 AVERAGE SCORES');
  console.log('==================');
  Object.entries(summary.averageScores).forEach(([category, score]) => {
    const status = score >= config.thresholds[category] ? '✅' : '❌';
    console.log(`${status} ${category}: ${score} (target: ${config.thresholds[category]})`);
  });

  // Print failed thresholds
  if (summary.failedThresholds.length > 0) {
    console.log('\n⚠️  FAILED THRESHOLDS');
    console.log('=====================');
    summary.failedThresholds.forEach(failure => {
      console.log(`❌ ${failure.category}: ${failure.score} (need ${failure.difference} more points)`);
    });
  }

  // Print recommendations
  if (summary.recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS');
    console.log('==================');
    summary.recommendations.forEach(rec => {
      console.log(`${rec.priority === 'High' ? '🔴' : '🟡'} ${rec.category}: ${rec.suggestion}`);
    });
  }

  // Overall status
  const overallPass = summary.failedThresholds.length === 0;
  console.log(`\n🏆 OVERALL STATUS: ${overallPass ? '✅ PASSED' : '❌ NEEDS IMPROVEMENT'}`);
  
  if (overallPass) {
    console.log('🎉 Congratulations! Your application meets all performance thresholds.');
  } else {
    console.log('📝 Please address the failed thresholds and recommendations above.');
  }
}

async function main() {
  console.log('🚀 Starting Performance Audit...');
  console.log(`📊 Testing ${config.urls.length} URLs`);
  
  let chrome;
  const results = [];

  try {
    // Launch Chrome
    chrome = await launchChrome();
    console.log(`🌐 Chrome launched on port ${chrome.port}`);

    // Run audits for each URL
    for (let i = 0; i < config.urls.length; i++) {
      const url = config.urls[i];
      try {
        const result = await runLighthouseAudit(url, chrome);
        const savedResult = saveReport(result, url, i);
        results.push(savedResult);
      } catch (error) {
        console.error(`❌ Failed to audit ${url}:`, error.message);
      }
    }

    // Generate and save summary
    const summary = generateSummaryReport(results);
    const summaryPath = path.join(config.outputDir, `summary_${new Date().toISOString().split('T')[0]}.json`);
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));

    // Print results
    printResults(results, summary);

    console.log(`\n📁 All reports saved to: ${config.outputDir}`);
    
    // Exit with appropriate code
    process.exit(summary.failedThresholds.length === 0 ? 0 : 1);

  } catch (error) {
    console.error('💥 Audit failed:', error);
    process.exit(1);
  } finally {
    if (chrome) {
      await chrome.kill();
    }
  }
}

// Handle CLI arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
PHCityRent Performance Audit Tool

Usage: node performance-audit.js [options]

Options:
  --help, -h     Show this help message
  --mobile       Run mobile audit instead of desktop
  --fast         Use faster throttling settings
  --urls         Comma-separated list of URLs to test

Examples:
  node performance-audit.js
  node performance-audit.js --mobile
  node performance-audit.js --urls "http://localhost:8080,http://localhost:8080/properties"
  `);
  process.exit(0);
}

// Handle mobile flag
if (process.argv.includes('--mobile')) {
  config.lighthouse.settings.formFactor = 'mobile';
  config.lighthouse.settings.screenEmulation.mobile = true;
  config.lighthouse.settings.screenEmulation.width = 375;
  config.lighthouse.settings.screenEmulation.height = 667;
  console.log('📱 Running mobile audit');
}

// Handle fast flag
if (process.argv.includes('--fast')) {
  config.lighthouse.settings.throttling = {
    rttMs: 0,
    throughputKbps: 0,
    cpuSlowdownMultiplier: 1,
  };
  console.log('⚡ Using fast throttling settings');
}

// Handle custom URLs
const urlsIndex = process.argv.indexOf('--urls');
if (urlsIndex !== -1 && process.argv[urlsIndex + 1]) {
  config.urls = process.argv[urlsIndex + 1].split(',');
  console.log(`🎯 Testing custom URLs: ${config.urls.join(', ')}`);
}

// Run the audit
main().catch(console.error);
