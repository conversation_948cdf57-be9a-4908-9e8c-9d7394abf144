import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeign<PERSON>ey } from "typeorm";

export class CreateRefreshTokensTable1703002000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "refresh_tokens",
        columns: [
          {
            name: "id",
            type: "uuid",
            isPrimary: true,
            generationStrategy: "uuid",
            default: "uuid_generate_v4()",
          },
          {
            name: "token",
            type: "varchar",
            length: "500",
            isUnique: true,
          },
          {
            name: "user_id",
            type: "uuid",
          },
          {
            name: "expiresAt",
            type: "timestamp",
          },
          {
            name: "createdAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
          {
            name: "updatedAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
        ],
      })
    );

    await queryRunner.createIndex(
      "refresh_tokens",
      new TableIndex({
        name: "IDX_refresh_tokens_token",
        columnNames: ["token"],
      })
    );

    await queryRunner.createIndex(
      "refresh_tokens",
      new TableIndex({
        name: "IDX_refresh_tokens_user_id",
        columnNames: ["user_id"],
      })
    );

    await queryRunner.createIndex(
      "refresh_tokens",
      new TableIndex({
        name: "IDX_refresh_tokens_expiresAt",
        columnNames: ["expiresAt"],
      })
    );

    await queryRunner.createForeignKey(
      "refresh_tokens",
      new TableForeignKey({
        name: "FK_refresh_tokens_user",
        columnNames: ["user_id"],
        referencedTableName: "users",
        referencedColumnNames: ["id"],
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey("refresh_tokens", "FK_refresh_tokens_user");
    await queryRunner.dropIndex("refresh_tokens", "IDX_refresh_tokens_expiresAt");
    await queryRunner.dropIndex("refresh_tokens", "IDX_refresh_tokens_user_id");
    await queryRunner.dropIndex("refresh_tokens", "IDX_refresh_tokens_token");
    await queryRunner.dropTable("refresh_tokens");
  }
}
