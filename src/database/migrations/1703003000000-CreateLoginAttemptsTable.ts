import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeign<PERSON>ey } from "typeorm";

export class CreateLoginAttemptsTable1703003000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "login_attempts",
        columns: [
          {
            name: "id",
            type: "uuid",
            isPrimary: true,
            generationStrategy: "uuid",
            default: "uuid_generate_v4()",
          },
          {
            name: "user_id",
            type: "uuid",
            isNullable: true,
          },
          {
            name: "ip_address",
            type: "varchar",
            length: "45",
          },
          {
            name: "user_agent",
            type: "varchar",
            length: "500",
            isNullable: true,
          },
          {
            name: "success",
            type: "boolean",
            default: false,
          },
          {
            name: "attempt_date",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
          {
            name: "createdAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
          {
            name: "updatedAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
        ],
      })
    );

    await queryRunner.createIndex(
      "login_attempts",
      new TableIndex({
        name: "IDX_login_attempts_user_id",
        columnNames: ["user_id"],
      })
    );

    await queryRunner.createIndex(
      "login_attempts",
      new TableIndex({
        name: "IDX_login_attempts_ip_address",
        columnNames: ["ip_address"],
      })
    );

    await queryRunner.createIndex(
      "login_attempts",
      new TableIndex({
        name: "IDX_login_attempts_success",
        columnNames: ["success"],
      })
    );

    await queryRunner.createIndex(
      "login_attempts",
      new TableIndex({
        name: "IDX_login_attempts_attempt_date",
        columnNames: ["attempt_date"],
      })
    );

    await queryRunner.createForeignKey(
      "login_attempts",
      new TableForeignKey({
        name: "FK_login_attempts_user",
        columnNames: ["user_id"],
        referencedTableName: "users",
        referencedColumnNames: ["id"],
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey("login_attempts", "FK_login_attempts_user");
    await queryRunner.dropIndex("login_attempts", "IDX_login_attempts_attempt_date");
    await queryRunner.dropIndex("login_attempts", "IDX_login_attempts_success");
    await queryRunner.dropIndex("login_attempts", "IDX_login_attempts_ip_address");
    await queryRunner.dropIndex("login_attempts", "IDX_login_attempts_user_id");
    await queryRunner.dropTable("login_attempts");
  }
}
