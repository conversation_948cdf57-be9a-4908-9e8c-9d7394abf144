import moment from 'moment-timezone';

export class DateUtil {
  static getTimezoneOffset(timezone: string): number {
    return moment.tz(timezone).utcOffset();
  }

  static convertToTimezone(date: Date, timezone: string): Date {
    return moment(date).tz(timezone).toDate();
  }

  static formatDate(date: Date, format: string = 'YYYY-MM-DD'): string {
    return moment(date).format(format);
  }

  static parseDate(dateString: string, format: string = 'YYYY-MM-DD'): Date {
    return moment(dateString, format).toDate();
  }

  static isValidDate(date: any): boolean {
    return moment(date).isValid();
  }

  static addDays(date: Date, days: number): Date {
    return moment(date).add(days, 'days').toDate();
  }

  static subtractDays(date: Date, days: number): Date {
    return moment(date).subtract(days, 'days').toDate();
  }

  static startOfDay(date: Date): Date {
    return moment(date).startOf('day').toDate();
  }

  static endOfDay(date: Date): Date {
    return moment(date).endOf('day').toDate();
  }
}
