import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { User } from "../users/entities/user.entity";
import { RegisterDto } from "./dto/register.dto";
import { UserRole } from "../users/enums/user-role.enum";
import * as bcrypt from "bcrypt";

export interface AuthResponse {
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
  user: Partial<User>;
}

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    const user = new User();
    user.email = registerDto.email;
    user.password = await this.hashPassword(registerDto.password);
    user.firstName = registerDto.firstName;
    user.lastName = registerDto.lastName;
    user.role = UserRole.TENANT;

    const savedUser = await this.userRepository.save(user);
    const tokens = await this.generateTokens(savedUser);

    return {
      tokens,
      user: this.sanitizeUser(savedUser),
    };
  }

  private async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt();
    return bcrypt.hash(password, salt);
  }

  private async generateTokens(user: User): Promise<{ accessToken: string; refreshToken: string }> {
    // Implementation of token generation
    return {
      accessToken: "access_token",
      refreshToken: "refresh_token",
    };
  }

  private sanitizeUser(user: User): Partial<User> {
    const { password, ...sanitizedUser } = user;
    return sanitizedUser;
  }
}
